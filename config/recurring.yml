# examples:
#   periodic_cleanup:
#     class: CleanSoftDeletedRecordsJob
#     queue: background
#     args: [ 1000, { batch_size: 500 } ]
#     schedule: every hour
#   periodic_cleanup_with_command:
#     command: "SoftDeletedRecord.due.delete_all"
#     priority: 2
#     schedule: at 5am every day

production:
  clear_solid_queue_finished_jobs:
    command: "SolidQueue::Job.clear_finished_in_batches(sleep_between_batches: 0.3)"
    schedule: every hour at minute 12
  
  # Notification scheduling jobs
  daily_digest_notifications:
    command: "NotificationSchedulerService.schedule_daily_digests"
    schedule: every day at 9:00am
  
  weekly_summary_notifications:
    command: "NotificationSchedulerService.schedule_weekly_summaries"
    schedule: every Monday at 9:00am
  
  monthly_report_notifications:
    command: "NotificationSchedulerService.schedule_monthly_reports"
    schedule: every month on the 1st at 9:00am
  
  form_abandonment_alerts:
    command: "NotificationSchedulerService.check_form_abandonment_alerts"
    schedule: every 4 hours
  
  conversion_rate_alerts:
    command: "NotificationSchedulerService.check_conversion_rate_drops"
    schedule: every 6 hours
  
  low_traffic_alerts:
    command: "NotificationSchedulerService.check_low_traffic_alerts"
    schedule: every day at 6:00pm
  
  new_insight_notifications:
    command: "NotificationSchedulerService.notify_new_insights"
    schedule: every hour at minute 30
