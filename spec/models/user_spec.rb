require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'validations' do
    it 'validates timezone presence' do
      user = build(:user, timezone: nil)
      expect(user).not_to be_valid
      expect(user.errors[:timezone]).to include("can't be blank")
    end

    it 'validates timezone is a valid timezone' do
      user = build(:user, timezone: 'Invalid/Timezone')
      expect(user).not_to be_valid
      expect(user.errors[:timezone]).to include("must be a valid timezone")
    end

    it 'accepts valid timezones' do
      user = build(:user, timezone: 'America/New_York')
      expect(user).to be_valid
    end
  end

  describe 'defaults' do
    it 'defaults timezone to UTC' do
      user = create(:user)
      expect(user.timezone).to eq('UTC')
    end
  end
end
