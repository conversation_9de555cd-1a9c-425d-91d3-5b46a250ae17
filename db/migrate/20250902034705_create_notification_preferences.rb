class CreateNotificationPreferences < ActiveRecord::Migration[8.0]
  def change
    create_table :notification_preferences do |t|
      t.references :user, null: false, foreign_key: true, index: { unique: true }
      
      # Email notification preferences
      t.boolean :email_insights, default: true
      t.boolean :email_reports, default: true
      t.boolean :email_alerts, default: true
      t.boolean :email_form_abandonment, default: true
      t.boolean :email_weekly_summary, default: true
      
      # Slack integration settings
      t.boolean :slack_integration, default: false
      t.string :slack_webhook_url
      t.string :slack_channel, default: '#general'
      t.boolean :slack_insights, default: false
      t.boolean :slack_alerts, default: false
      t.boolean :slack_reports, default: false
      
      # Webhook notifications
      t.boolean :webhook_notifications, default: false
      t.string :webhook_url
      t.json :webhook_events, default: '[]'
      
      # Frequency settings
      t.boolean :daily_digest, default: false
      t.boolean :weekly_digest, default: true
      t.boolean :monthly_report, default: true
      
      # Alert thresholds
      t.integer :alert_form_abandonment_threshold, default: 70
      t.integer :alert_conversion_drop_threshold, default: 20
      t.integer :alert_low_traffic_threshold, default: 10

      t.timestamps
    end
  end
end
