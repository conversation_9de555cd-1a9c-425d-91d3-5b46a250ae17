class ChangeAccountIdToUuidInUsers < ActiveRecord::Migration[8.0]
  def up
    # Remove the old integer account_id column
    remove_column :users, :account_id
    
    # Add a new UUID account_id column with proper foreign key
    add_reference :users, :account, type: :uuid, foreign_key: true, null: true
  end
  
  def down
    # Remove the UUID account_id column
    remove_reference :users, :account, foreign_key: true
    
    # Add back the integer account_id column
    add_reference :users, :account, null: true
  end
end
