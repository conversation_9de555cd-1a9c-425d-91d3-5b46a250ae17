# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_09_02_035348) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"
  enable_extension "pgcrypto"

  create_table "accounts", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.string "name", null: false
    t.string "slug", null: false
    t.string "billing_email"
    t.string "timezone", default: "UTC", null: false
    t.datetime "trial_ends_at"
    t.string "plan", default: "free"
    t.integer "status", default: 0
    t.jsonb "settings", default: {}
    t.integer "monthly_tracked_events", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["name"], name: "index_accounts_on_name"
    t.index ["slug"], name: "index_accounts_on_slug", unique: true
    t.index ["status"], name: "index_accounts_on_status"
    t.index ["trial_ends_at"], name: "index_accounts_on_trial_ends_at"
  end

  create_table "api_keys", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "account_id", null: false
    t.string "name", null: false
    t.string "key", null: false
    t.datetime "last_used_at"
    t.datetime "expires_at"
    t.boolean "active", default: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "permissions", default: ["read"], null: false
    t.integer "request_count", default: 0
    t.integer "recent_request_count", default: 0
    t.integer "rate_limit_hits", default: 0
    t.bigint "created_by_id"
    t.datetime "revoked_at"
    t.string "full_key"
    t.index ["account_id"], name: "index_api_keys_on_account_id"
    t.index ["active"], name: "index_api_keys_on_active"
    t.index ["created_by_id"], name: "index_api_keys_on_created_by_id"
    t.index ["key"], name: "index_api_keys_on_key", unique: true
    t.index ["permissions"], name: "index_api_keys_on_permissions", using: :gin
    t.index ["revoked_at"], name: "index_api_keys_on_revoked_at"
  end

  create_table "field_events", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "form_session_id", null: false
    t.uuid "form_field_id", null: false
    t.string "event_type", null: false
    t.datetime "timestamp", null: false
    t.integer "duration"
    t.integer "value_length"
    t.text "value"
    t.text "error_message"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["event_type"], name: "index_field_events_on_event_type"
    t.index ["form_field_id", "event_type"], name: "index_field_events_on_form_field_id_and_event_type"
    t.index ["form_field_id"], name: "index_field_events_on_form_field_id"
    t.index ["form_session_id", "timestamp"], name: "index_field_events_on_form_session_id_and_timestamp"
    t.index ["form_session_id"], name: "index_field_events_on_form_session_id"
  end

  create_table "form_fields", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "form_id", null: false
    t.string "name", null: false
    t.string "field_type"
    t.string "label"
    t.string "field_identifier"
    t.integer "position"
    t.boolean "required", default: false
    t.integer "interaction_count", default: 0
    t.integer "error_count", default: 0
    t.integer "correction_count", default: 0
    t.float "avg_interaction_time"
    t.float "error_rate"
    t.float "correction_rate"
    t.float "abandonment_rate"
    t.jsonb "validation_rules", default: {}
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["abandonment_rate"], name: "index_form_fields_on_abandonment_rate"
    t.index ["error_rate"], name: "index_form_fields_on_error_rate"
    t.index ["form_id", "name"], name: "index_form_fields_on_form_id_and_name", unique: true
    t.index ["form_id", "position"], name: "index_form_fields_on_form_id_and_position"
    t.index ["form_id"], name: "index_form_fields_on_form_id"
  end

  create_table "form_sessions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "form_id", null: false
    t.string "visitor_id", null: false
    t.string "session_id", null: false
    t.datetime "started_at", null: false
    t.datetime "completed_at"
    t.datetime "abandoned_at"
    t.string "status", default: "in_progress", null: false
    t.integer "interaction_count", default: 0
    t.float "time_spent"
    t.string "device_type"
    t.string "browser"
    t.string "operating_system"
    t.string "country"
    t.string "city"
    t.text "referrer"
    t.text "user_agent"
    t.string "ip_address"
    t.jsonb "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "session_identifier"
    t.string "url"
    t.index ["form_id", "started_at"], name: "index_form_sessions_on_form_id_and_started_at"
    t.index ["form_id"], name: "index_form_sessions_on_form_id"
    t.index ["session_id"], name: "index_form_sessions_on_session_id", unique: true
    t.index ["session_identifier"], name: "index_form_sessions_on_session_identifier"
    t.index ["status"], name: "index_form_sessions_on_status"
    t.index ["visitor_id"], name: "index_form_sessions_on_visitor_id"
  end

  create_table "form_submissions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "form_session_id", null: false
    t.uuid "form_id", null: false
    t.datetime "submitted_at", null: false
    t.float "completion_time"
    t.jsonb "form_data", default: {}
    t.jsonb "metadata", default: {}
    t.string "visitor_id"
    t.string "ip_address"
    t.string "user_agent"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["form_id"], name: "index_form_submissions_on_form_id"
    t.index ["form_session_id"], name: "index_form_submissions_on_form_session_id"
    t.index ["submitted_at"], name: "index_form_submissions_on_submitted_at"
    t.index ["visitor_id"], name: "index_form_submissions_on_visitor_id"
  end

  create_table "forms", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "website_id", null: false
    t.string "form_identifier", null: false
    t.string "name"
    t.text "url", null: false
    t.string "form_type"
    t.integer "field_count", default: 0
    t.integer "total_sessions", default: 0
    t.integer "total_submissions", default: 0
    t.float "avg_completion_time"
    t.float "conversion_rate"
    t.float "abandonment_rate"
    t.jsonb "field_metadata", default: {}
    t.jsonb "settings", default: {}
    t.datetime "first_seen_at"
    t.datetime "last_seen_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["abandonment_rate"], name: "index_forms_on_abandonment_rate"
    t.index ["conversion_rate"], name: "index_forms_on_conversion_rate"
    t.index ["last_seen_at"], name: "index_forms_on_last_seen_at"
    t.index ["website_id", "form_identifier"], name: "index_forms_on_website_id_and_form_identifier", unique: true
    t.index ["website_id", "url"], name: "index_forms_on_website_id_and_url"
    t.index ["website_id"], name: "index_forms_on_website_id"
  end

  create_table "insights", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "form_id", null: false
    t.string "type", null: false
    t.string "severity", null: false
    t.string "title", null: false
    t.text "description"
    t.text "recommendation"
    t.integer "impact_score"
    t.jsonb "metadata", default: {}
    t.datetime "detected_at"
    t.datetime "resolved_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "notified_at"
    t.index ["form_id", "type"], name: "index_insights_on_form_id_and_type"
    t.index ["form_id"], name: "index_insights_on_form_id"
    t.index ["metadata"], name: "index_insights_on_metadata", using: :gin
    t.index ["resolved_at"], name: "index_insights_on_resolved_at"
    t.index ["severity"], name: "index_insights_on_severity"
  end

  create_table "notification_preferences", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.boolean "email_insights", default: true
    t.boolean "email_reports", default: true
    t.boolean "email_alerts", default: true
    t.boolean "email_form_abandonment", default: true
    t.boolean "email_weekly_summary", default: true
    t.boolean "slack_integration", default: false
    t.string "slack_webhook_url"
    t.string "slack_channel", default: "#general"
    t.boolean "slack_insights", default: false
    t.boolean "slack_alerts", default: false
    t.boolean "slack_reports", default: false
    t.boolean "webhook_notifications", default: false
    t.string "webhook_url"
    t.json "webhook_events", default: "[]"
    t.boolean "daily_digest", default: false
    t.boolean "weekly_digest", default: true
    t.boolean "monthly_report", default: true
    t.integer "alert_form_abandonment_threshold", default: 70
    t.integer "alert_conversion_drop_threshold", default: 20
    t.integer "alert_low_traffic_threshold", default: 10
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_notification_preferences_on_user_id", unique: true
  end

  create_table "subscriptions", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "account_id", null: false
    t.string "plan_type", default: "starter", null: false
    t.string "status", default: "trialing", null: false
    t.datetime "current_period_start"
    t.datetime "current_period_end"
    t.string "stripe_subscription_id"
    t.string "stripe_customer_id"
    t.integer "monthly_session_limit"
    t.integer "monthly_sessions_used", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account_id"], name: "index_subscriptions_on_account_id", unique: true
    t.index ["current_period_end"], name: "index_subscriptions_on_current_period_end"
    t.index ["status"], name: "index_subscriptions_on_status"
    t.index ["stripe_subscription_id"], name: "index_subscriptions_on_stripe_subscription_id"
  end

  create_table "team_members", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "account_id", null: false
    t.string "email", null: false
    t.string "name"
    t.string "role", default: "member", null: false
    t.string "invitation_token"
    t.datetime "invitation_sent_at"
    t.datetime "invitation_accepted_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "invited_by_id"
    t.text "message"
    t.bigint "user_id"
    t.index ["account_id", "email"], name: "index_team_members_on_account_id_and_email", unique: true
    t.index ["account_id"], name: "index_team_members_on_account_id"
    t.index ["invitation_token"], name: "index_team_members_on_invitation_token", unique: true
    t.index ["invited_by_id"], name: "index_team_members_on_invited_by_id"
    t.index ["role"], name: "index_team_members_on_role"
    t.index ["user_id"], name: "index_team_members_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at"
    t.datetime "remember_created_at"
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at"
    t.datetime "last_sign_in_at"
    t.string "current_sign_in_ip"
    t.string "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at"
    t.datetime "confirmation_sent_at"
    t.string "unconfirmed_email"
    t.integer "failed_attempts", default: 0, null: false
    t.string "unlock_token"
    t.datetime "locked_at"
    t.string "first_name"
    t.string "last_name"
    t.string "role"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "timezone", default: "UTC"
    t.uuid "account_id"
    t.index ["account_id"], name: "index_users_on_account_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "websites", id: :uuid, default: -> { "gen_random_uuid()" }, force: :cascade do |t|
    t.uuid "account_id", null: false
    t.string "domain", null: false
    t.string "name", null: false
    t.string "tracking_id", null: false
    t.string "verification_token"
    t.datetime "verified_at"
    t.integer "status", default: 0
    t.jsonb "settings", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "tracking_token"
    t.integer "total_page_views"
    t.index ["account_id", "domain"], name: "index_websites_on_account_id_and_domain", unique: true
    t.index ["account_id"], name: "index_websites_on_account_id"
    t.index ["status"], name: "index_websites_on_status"
    t.index ["tracking_id"], name: "index_websites_on_tracking_id", unique: true
    t.index ["tracking_token"], name: "index_websites_on_tracking_token", unique: true
    t.index ["verified_at"], name: "index_websites_on_verified_at"
  end

  add_foreign_key "api_keys", "accounts"
  add_foreign_key "field_events", "form_fields"
  add_foreign_key "field_events", "form_sessions"
  add_foreign_key "form_fields", "forms"
  add_foreign_key "form_sessions", "forms"
  add_foreign_key "form_submissions", "form_sessions"
  add_foreign_key "form_submissions", "forms"
  add_foreign_key "forms", "websites"
  add_foreign_key "insights", "forms"
  add_foreign_key "notification_preferences", "users"
  add_foreign_key "subscriptions", "accounts"
  add_foreign_key "team_members", "accounts"
  add_foreign_key "team_members", "users"
  add_foreign_key "users", "accounts"
  add_foreign_key "websites", "accounts"
end
