<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Abandonment Alert - FormFlow Pro</title>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; margin: 0; padding: 0; background-color: #f9fafb; }
      .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
      .header { background: linear-gradient(135deg, #f56565 0%, #c53030 100%); color: white; text-align: center; padding: 2rem; }
      .alert-icon { font-size: 3rem; margin-bottom: 1rem; }
      .content { padding: 2rem; }
      .alert-card { background-color: #fed7d7; border: 1px solid #feb2b2; border-radius: 8px; padding: 1.5rem; margin-bottom: 1.5rem; }
      .metric { background-color: #f7fafc; border-radius: 8px; padding: 1rem; margin: 1rem 0; text-align: center; }
      .metric-value { font-size: 2.5rem; font-weight: bold; color: #c53030; }
      .metric-label { color: #718096; font-size: 0.875rem; margin-top: 0.5rem; }
      .form-info { background-color: #edf2f7; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0; }
      .sessions-list { background-color: #f7fafc; border-radius: 8px; padding: 1rem; margin: 1rem 0; }
      .session-item { padding: 0.5rem 0; border-bottom: 1px solid #e2e8f0; display: flex; justify-content: space-between; }
      .session-item:last-child { border-bottom: none; }
      .footer { background-color: #2d3748; color: #a0aec0; text-align: center; padding: 2rem; }
      .footer a { color: #63b3ed; text-decoration: none; }
      .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 1rem 0; }
      .urgent { color: #c53030; font-weight: bold; }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Header -->
      <div class="header">
        <div class="alert-icon">🚨</div>
        <h1>High Form Abandonment Alert</h1>
        <p>Immediate attention required</p>
      </div>

      <!-- Content -->
      <div class="content">
        <div class="alert-card">
          <h2 style="margin-top: 0; color: #c53030;">⚠️ Critical Alert</h2>
          <p><strong><%= @form.name %></strong> on <strong><%= @form.website.domain %></strong> has exceeded your abandonment threshold.</p>
        </div>

        <p>Hi <%= @user.first_name %>,</p>
        <p>We've detected a concerning trend with one of your forms that requires immediate attention.</p>

        <!-- Key Metrics -->
        <div class="metric">
          <div class="metric-value"><%= @abandonment_rate.round(1) %>%</div>
          <div class="metric-label">Current Abandonment Rate</div>
          <div style="color: #718096; margin-top: 0.5rem; font-size: 0.875rem;">
            Threshold: <%= @threshold %>% • 
            <span class="urgent">+<%= (@abandonment_rate - @threshold).round(1) %>% over limit</span>
          </div>
        </div>

        <!-- Form Information -->
        <div class="form-info">
          <h3 style="margin-top: 0;">📋 Form Details</h3>
          <p><strong>Form Name:</strong> <%= @form.name %></p>
          <p><strong>Website:</strong> <%= @form.website.domain %></p>
          <p><strong>Current Status:</strong> <span class="urgent">Needs Immediate Attention</span></p>
          <% if @form.description.present? %>
            <p><strong>Description:</strong> <%= @form.description %></p>
          <% end %>
        </div>

        <!-- Recent Abandoned Sessions -->
        <% if @recent_sessions.any? %>
          <h3>📊 Recent Abandoned Sessions</h3>
          <div class="sessions-list">
            <% @recent_sessions.limit(5).each do |session| %>
              <div class="session-item">
                <span>
                  <%= session.created_at.strftime("%m/%d %H:%M") %>
                  <% if session.time_spent %>
                    • <%= (session.time_spent / 60).round(1) %>min
                  <% end %>
                </span>
                <span style="color: #c53030;">Abandoned</span>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Recommendations -->
        <div style="background-color: #e6fffa; border-left: 4px solid #38b2ac; padding: 1.5rem; margin: 1.5rem 0; border-radius: 0 8px 8px 0;">
          <h3 style="margin-top: 0; color: #234e52;">💡 Recommended Actions</h3>
          <ul style="margin-bottom: 0;">
            <li>Review form length and complexity - shorter forms typically have lower abandonment rates</li>
            <li>Check for technical issues or slow loading times</li>
            <li>Analyze where users are dropping off in the form flow</li>
            <li>Consider implementing progress indicators or step-by-step guidance</li>
            <li>Test form on different devices and browsers</li>
          </ul>
        </div>

        <!-- Call to Action -->
        <div style="text-align: center; margin: 2rem 0;">
          <a href="<%= dashboard_url(host: request.host_with_port) %>" class="cta-button">
            Analyze Form Performance
          </a>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p>This alert was sent because your form abandonment rate exceeded <%= @threshold %>%</p>
        <p>
          <a href="<%= settings_notifications_url(host: request.host_with_port) %>">Manage alert thresholds</a> | 
          <a href="<%= dashboard_url(host: request.host_with_port) %>">View Dashboard</a>
        </p>
        <p style="font-size: 0.75rem; margin-top: 1rem;">
          © <%= Date.current.year %> FormFlow Pro. All rights reserved.
        </p>
      </div>
    </div>
  </body>
</html>