<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FormFlow Pro Daily Digest</title>
    <style>
      body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; margin: 0; padding: 0; background-color: #f9fafb; }
      .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
      .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; padding: 2rem; }
      .content { padding: 2rem; }
      .metric-card { background-color: #f8fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem; }
      .metric-value { font-size: 2rem; font-weight: bold; color: #1a202c; }
      .metric-label { color: #718096; font-size: 0.875rem; text-transform: uppercase; letter-spacing: 0.05em; }
      .metric-change { font-size: 0.875rem; margin-top: 0.25rem; }
      .positive { color: #38a169; }
      .negative { color: #e53e3e; }
      .neutral { color: #718096; }
      .insight { background-color: #ebf8ff; border-left: 4px solid #3182ce; padding: 1rem; margin-bottom: 1rem; border-radius: 0 8px 8px 0; }
      .footer { background-color: #2d3748; color: #a0aec0; text-align: center; padding: 2rem; }
      .footer a { color: #63b3ed; text-decoration: none; }
      .cta-button { display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 1rem 0; }
      .grid { display: flex; gap: 1rem; flex-wrap: wrap; }
      .grid-item { flex: 1; min-width: 200px; }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- Header -->
      <div class="header">
        <h1>📊 Daily Digest</h1>
        <p><%= @date.strftime("%A, %B %d, %Y") %></p>
      </div>

      <!-- Content -->
      <div class="content">
        <p>Hi <%= @user.first_name %>,</p>
        <p>Here's your daily FormFlow Pro summary for <%= @account.name %>:</p>

        <!-- Key Metrics -->
        <div class="grid">
          <div class="grid-item">
            <div class="metric-card">
              <div class="metric-value"><%= @metrics[:today_sessions] %></div>
              <div class="metric-label">Sessions Today</div>
              <div class="metric-change <%= @metrics[:today_sessions] >= @metrics[:yesterday_sessions] ? 'positive' : 'negative' %>">
                <% change = @metrics[:yesterday_sessions] > 0 ? ((@metrics[:today_sessions] - @metrics[:yesterday_sessions]).to_f / @metrics[:yesterday_sessions] * 100).round(1) : 0 %>
                <%= change >= 0 ? '+' : '' %><%= change %>% from yesterday
              </div>
            </div>
          </div>
          
          <div class="grid-item">
            <div class="metric-card">
              <div class="metric-value"><%= @metrics[:today_conversions] %></div>
              <div class="metric-label">Conversions Today</div>
              <div class="metric-change <%= @metrics[:today_conversions] >= @metrics[:yesterday_conversions] ? 'positive' : 'negative' %>">
                <% change = @metrics[:yesterday_conversions] > 0 ? ((@metrics[:today_conversions] - @metrics[:yesterday_conversions]).to_f / @metrics[:yesterday_conversions] * 100).round(1) : 0 %>
                <%= change >= 0 ? '+' : '' %><%= change %>% from yesterday
              </div>
            </div>
          </div>
        </div>

        <div class="grid">
          <div class="grid-item">
            <div class="metric-card">
              <div class="metric-value"><%= @metrics[:active_forms] %>/<%= @metrics[:forms_count] %></div>
              <div class="metric-label">Active Forms</div>
              <div class="metric-change neutral">Forms with activity today</div>
            </div>
          </div>
        </div>

        <!-- Recent Insights -->
        <% if @insights.any? %>
          <h2>🔍 New Insights</h2>
          <% @insights.each do |insight| %>
            <div class="insight">
              <strong><%= insight.form.name %></strong><br>
              <%= insight.description %>
              <% if insight.recommendations.present? %>
                <br><em>💡 <%= insight.recommendations %></em>
              <% end %>
            </div>
          <% end %>
        <% end %>

        <!-- Call to Action -->
        <div style="text-align: center; margin: 2rem 0;">
          <a href="<%= dashboard_url(host: request.host_with_port) %>" class="cta-button">
            View Full Dashboard
          </a>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p>This is your daily digest from <strong>FormFlow Pro</strong></p>
        <p>
          <a href="<%= settings_notifications_url(host: request.host_with_port) %>">Manage notification preferences</a> | 
          <a href="<%= dashboard_url(host: request.host_with_port) %>">View Dashboard</a>
        </p>
        <p style="font-size: 0.75rem; margin-top: 1rem;">
          © <%= Date.current.year %> FormFlow Pro. All rights reserved.
        </p>
      </div>
    </div>
  </body>
</html>