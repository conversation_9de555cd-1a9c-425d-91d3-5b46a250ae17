<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">API Documentation</h1>
            <p class="mt-1 text-sm text-gray-600">Complete guide to using the FormFlow Pro API</p>
          </div>
          <%= link_to "Back to Settings", settings_api_keys_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
      <!-- Table of Contents -->
      <div class="lg:col-span-1">
        <div class="bg-white shadow-sm rounded-lg p-6 sticky top-8">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Table of Contents</h3>
          <nav class="space-y-2">
            <a href="#getting-started" class="block text-sm text-indigo-600 hover:text-indigo-800">Getting Started</a>
            <a href="#authentication" class="block text-sm text-indigo-600 hover:text-indigo-800">Authentication</a>
            <a href="#rate-limits" class="block text-sm text-indigo-600 hover:text-indigo-800">Rate Limits</a>
            <a href="#tracking-api" class="block text-sm text-indigo-600 hover:text-indigo-800">Tracking API</a>
            <a href="#analytics-api" class="block text-sm text-indigo-600 hover:text-indigo-800">Analytics API</a>
            <a href="#webhooks" class="block text-sm text-indigo-600 hover:text-indigo-800">Webhooks</a>
            <a href="#errors" class="block text-sm text-indigo-600 hover:text-indigo-800">Error Handling</a>
            <a href="#examples" class="block text-sm text-indigo-600 hover:text-indigo-800">Code Examples</a>
          </nav>
        </div>
      </div>

      <!-- Main Content -->
      <div class="lg:col-span-3 space-y-8">
        <!-- Getting Started -->
        <section id="getting-started" class="bg-white shadow-sm rounded-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Getting Started</h2>
          <p class="text-gray-600 mb-4">
            The FormFlow Pro API provides programmatic access to your form analytics data. Use it to integrate 
            form insights into your applications, build custom dashboards, or automate reporting workflows.
          </p>
          
          <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Base URL</h3>
                <p class="mt-2 text-sm text-blue-700">
                  <code class="bg-blue-100 px-2 py-1 rounded text-xs"><%= request.base_url %>/api/v1</code>
                </p>
              </div>
            </div>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-3">Quick Start</h3>
          <ol class="list-decimal list-inside space-y-2 text-gray-600">
            <li><%= link_to "Create an API key", settings_api_keys_path, class: "text-indigo-600 hover:text-indigo-800" %> with the appropriate permissions</li>
            <li>Include the API key in your requests using the <code class="bg-gray-100 px-1 rounded">Authorization</code> header</li>
            <li>Make requests to the endpoints documented below</li>
            <li>Handle responses and errors appropriately</li>
          </ol>
        </section>

        <!-- Authentication -->
        <section id="authentication" class="bg-white shadow-sm rounded-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Authentication</h2>
          <p class="text-gray-600 mb-4">
            FormFlow Pro uses API keys for authentication. Include your API key in the Authorization header of every request.
          </p>

          <h3 class="text-lg font-medium text-gray-900 mb-3">Header Format</h3>
          <div class="bg-gray-900 rounded-lg p-4 mb-4">
            <code class="text-green-400 text-sm">Authorization: Bearer YOUR_API_KEY</code>
          </div>

          <% if @api_keys.any? %>
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">Your API Keys</h3>
                  <div class="mt-2 text-sm text-yellow-700">
                    <p>You have <%= @api_keys.count %> active API key<%= @api_keys.count == 1 ? '' : 's' %>:</p>
                    <ul class="mt-2 list-disc list-inside">
                      <% @api_keys.each do |key| %>
                        <li><strong><%= key.name %></strong> - <%= key.permissions.join(', ') %> permissions</li>
                      <% end %>
                    </ul>
                    <p class="mt-2">
                      <%= link_to "Manage API Keys →", settings_api_keys_path, class: "font-medium underline hover:text-yellow-800" %>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </section>

        <!-- Rate Limits -->
        <section id="rate-limits" class="bg-white shadow-sm rounded-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Rate Limits</h2>
          <p class="text-gray-600 mb-4">
            API requests are subject to rate limiting to ensure fair usage and system stability.
          </p>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="border border-gray-200 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-indigo-600">60</div>
              <div class="text-sm text-gray-500">requests/minute</div>
            </div>
            <div class="border border-gray-200 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-indigo-600">1,000</div>
              <div class="text-sm text-gray-500">requests/hour</div>
            </div>
            <div class="border border-gray-200 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-indigo-600">10,000</div>
              <div class="text-sm text-gray-500">requests/day</div>
            </div>
          </div>

          <h3 class="text-lg font-medium text-gray-900 mb-3">Rate Limit Headers</h3>
          <p class="text-gray-600 mb-3">
            Each API response includes headers to help you track your usage:
          </p>
          <div class="bg-gray-900 rounded-lg p-4 text-sm">
            <div class="text-green-400">X-RateLimit-Limit: 60</div>
            <div class="text-green-400">X-RateLimit-Remaining: 58</div>
            <div class="text-green-400">X-RateLimit-Reset: 1640995200</div>
          </div>
        </section>

        <!-- Tracking API -->
        <section id="tracking-api" class="bg-white shadow-sm rounded-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Tracking API</h2>
          <p class="text-gray-600 mb-6">
            The Tracking API allows you to send form interaction data to FormFlow Pro programmatically.
          </p>

          <!-- Session Start -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Start a Session</h3>
            <div class="flex items-center mb-2">
              <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded mr-3">POST</span>
              <code class="text-sm">/api/v1/tracking/session_start</code>
            </div>
            
            <p class="text-gray-600 mb-4">Initialize a new form session for tracking user interactions.</p>
            
            <h4 class="font-medium text-gray-900 mb-2">Request Body</h4>
            <div class="bg-gray-900 rounded-lg p-4 mb-4 text-sm">
              <pre class="text-green-400">{
  "form_id": "uuid",
  "visitor_id": "string",
  "session_id": "string",
  "metadata": {
    "user_agent": "string",
    "ip_address": "string",
    "referrer": "string"
  }
}</pre>
            </div>

            <h4 class="font-medium text-gray-900 mb-2">Response</h4>
            <div class="bg-gray-900 rounded-lg p-4 text-sm">
              <pre class="text-blue-400">{
  "status": "success",
  "session_id": "sess_1234567890abcdef",
  "form_session_id": "uuid"
}</pre>
            </div>
          </div>

          <!-- Field Event -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Track Field Event</h3>
            <div class="flex items-center mb-2">
              <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded mr-3">POST</span>
              <code class="text-sm">/api/v1/tracking/field_event</code>
            </div>
            
            <p class="text-gray-600 mb-4">Record user interactions with form fields.</p>
            
            <h4 class="font-medium text-gray-900 mb-2">Request Body</h4>
            <div class="bg-gray-900 rounded-lg p-4 mb-4 text-sm">
              <pre class="text-green-400">{
  "session_id": "string",
  "field_name": "string",
  "event_type": "focus|blur|change|error",
  "timestamp": "2024-01-15T10:30:00Z",
  "duration": 1500,
  "value_length": 25,
  "error_message": "string" // optional
}</pre>
            </div>
          </div>

          <!-- Batch Events -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Batch Events</h3>
            <div class="flex items-center mb-2">
              <span class="bg-green-100 text-green-800 text-xs font-medium px-2 py-1 rounded mr-3">POST</span>
              <code class="text-sm">/api/v1/tracking/batch</code>
            </div>
            
            <p class="text-gray-600 mb-4">Send multiple events in a single request for better performance.</p>
            
            <h4 class="font-medium text-gray-900 mb-2">Request Body</h4>
            <div class="bg-gray-900 rounded-lg p-4 text-sm">
              <pre class="text-green-400">{
  "events": [
    {
      "type": "session_start",
      "data": { /* session data */ }
    },
    {
      "type": "field_event", 
      "data": { /* event data */ }
    }
  ]
}</pre>
            </div>
          </div>
        </section>

        <!-- Code Examples -->
        <section id="examples" class="bg-white shadow-sm rounded-lg p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Code Examples</h2>
          
          <!-- JavaScript -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-900 mb-3">JavaScript</h3>
            <div class="bg-gray-900 rounded-lg p-4 text-sm">
              <pre class="text-green-400">// Initialize a form session
async function startFormSession(formId, visitorId) {
  const response = await fetch('<%= request.base_url %>/api/v1/tracking/session_start', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      form_id: formId,
      visitor_id: visitorId,
      session_id: `sess_${Date.now()}`,
      metadata: {
        user_agent: navigator.userAgent,
        referrer: document.referrer
      }
    })
  });
  
  return await response.json();
}

// Track field interaction
async function trackFieldEvent(sessionId, fieldName, eventType) {
  await fetch('<%= request.base_url %>/api/v1/tracking/field_event', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer YOUR_API_KEY',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      session_id: sessionId,
      field_name: fieldName,
      event_type: eventType,
      timestamp: new Date().toISOString()
    })
  });
}</pre>
            </div>
          </div>
        </section>
      </div>
    </div>
  </div>
</div>

<!-- Smooth scrolling for anchor links -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });
});
</script>