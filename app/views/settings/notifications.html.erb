<div class="min-h-screen bg-gray-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="bg-white shadow-sm rounded-lg mb-8">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">Notification Settings</h1>
            <p class="mt-1 text-sm text-gray-600">Manage your email, Slack, and webhook notifications</p>
          </div>
          <%= link_to "Back to Settings", settings_path, class: "inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
    </div>

    <%= form_with(model: @notification_preference, url: settings_notifications_path, method: :patch, local: true, class: "space-y-8") do |form| %>
      <!-- Email Notifications Section -->
      <div class="bg-white shadow-sm rounded-lg">
        <div class="px-6 py-5 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 flex items-center">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
            </svg>
            Email Notifications
          </h2>
          <p class="mt-1 text-sm text-gray-600">Configure when you want to receive email notifications</p>
        </div>
        
        <div class="px-6 py-5 space-y-6">
          <!-- Email Preferences -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-4">
              <h3 class="text-sm font-medium text-gray-900">Content Notifications</h3>
              
              <div class="flex items-center">
                <%= form.check_box :email_insights, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">New insights and recommendations</label>
              </div>
              
              <div class="flex items-center">
                <%= form.check_box :email_form_abandonment, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Form abandonment alerts</label>
              </div>
              
              <div class="flex items-center">
                <%= form.check_box :email_alerts, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Performance alerts</label>
              </div>
            </div>
            
            <div class="space-y-4">
              <h3 class="text-sm font-medium text-gray-900">Report Notifications</h3>
              
              <div class="flex items-center">
                <%= form.check_box :daily_digest, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Daily digest</label>
              </div>
              
              <div class="flex items-center">
                <%= form.check_box :weekly_digest, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Weekly summary</label>
              </div>
              
              <div class="flex items-center">
                <%= form.check_box :monthly_report, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Monthly report</label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Alert Thresholds Section -->
      <div class="bg-white shadow-sm rounded-lg">
        <div class="px-6 py-5 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 flex items-center">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            Alert Thresholds
          </h2>
          <p class="mt-1 text-sm text-gray-600">Set when you want to receive alerts based on form performance</p>
        </div>
        
        <div class="px-6 py-5 space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-900">Form Abandonment Rate</label>
              <div class="mt-1 relative">
                <%= form.number_field :alert_form_abandonment_threshold, 
                    min: 1, max: 100, 
                    class: "block w-full pr-10 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">%</span>
                </div>
              </div>
              <p class="mt-1 text-xs text-gray-500">Alert when abandonment rate exceeds this threshold</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-900">Conversion Rate Drop</label>
              <div class="mt-1 relative">
                <%= form.number_field :alert_conversion_drop_threshold, 
                    min: 1, max: 100, 
                    class: "block w-full pr-10 border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">%</span>
                </div>
              </div>
              <p class="mt-1 text-xs text-gray-500">Alert when conversion rate drops by this percentage</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-900">Low Traffic Threshold</label>
              <div class="mt-1">
                <%= form.number_field :alert_low_traffic_threshold, 
                    min: 1, 
                    class: "block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              </div>
              <p class="mt-1 text-xs text-gray-500">Alert when daily sessions fall below this number</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Slack Integration Section -->
      <div class="bg-white shadow-sm rounded-lg">
        <div class="px-6 py-5 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 flex items-center">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M5.042 15.165a2.528 2.528 0 0 1-2.52-2.523A2.528 2.528 0 0 1 5.042 10.1a2.528 2.528 0 0 1 2.52 2.542 2.528 2.528 0 0 1-2.52 2.523Zm6.906-4.123a2.528 2.528 0 0 1-2.52-2.542A2.528 2.528 0 0 1 11.948 6a2.528 2.528 0 0 1 2.52 2.5 2.528 2.528 0 0 1-2.52 2.542Zm6.906 4.123a2.528 2.528 0 0 1-2.52-2.523 2.528 2.528 0 0 1 2.52-2.542 2.528 2.528 0 0 1 2.52 2.542 2.528 2.528 0 0 1-2.52 2.523Zm-6.906 4.123a2.528 2.528 0 0 1-2.52-2.542 2.528 2.528 0 0 1 2.52-2.523 2.528 2.528 0 0 1 2.52 2.523 2.528 2.528 0 0 1-2.52 2.542Z"/>
            </svg>
            Slack Integration
          </h2>
          <p class="mt-1 text-sm text-gray-600">Send notifications directly to your Slack channels</p>
        </div>
        
        <div class="px-6 py-5 space-y-6">
          <div class="flex items-center">
            <%= form.check_box :slack_integration, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded", id: "slack_integration_toggle" %>
            <label for="slack_integration_toggle" class="ml-2 block text-sm font-medium text-gray-900">Enable Slack notifications</label>
          </div>
          
          <div id="slack_settings" class="space-y-4" style="<%= 'display: none;' unless @notification_preference.slack_integration %>">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-900">Webhook URL</label>
                <%= form.url_field :slack_webhook_url, 
                    placeholder: "https://hooks.slack.com/services/...", 
                    class: "mt-1 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                <p class="mt-1 text-xs text-gray-500">
                  Get your webhook URL from 
                  <a href="https://api.slack.com/apps" target="_blank" class="text-indigo-600 hover:text-indigo-500">Slack App settings</a>
                </p>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-900">Channel</label>
                <%= form.text_field :slack_channel, 
                    placeholder: "#general", 
                    class: "mt-1 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
                <p class="mt-1 text-xs text-gray-500">Channel or user to send notifications to</p>
              </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="flex items-center">
                <%= form.check_box :slack_insights, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">New insights</label>
              </div>
              
              <div class="flex items-center">
                <%= form.check_box :slack_alerts, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Performance alerts</label>
              </div>
              
              <div class="flex items-center">
                <%= form.check_box :slack_reports, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
                <label class="ml-2 block text-sm text-gray-900">Daily summaries</label>
              </div>
            </div>
            
            <div class="pt-4">
              <button type="submit" name="test_slack" value="true" 
                      class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Test Slack Integration
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Webhook Integration Section -->
      <div class="bg-white shadow-sm rounded-lg">
        <div class="px-6 py-5 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 flex items-center">
            <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"/>
            </svg>
            Webhook Integration
          </h2>
          <p class="mt-1 text-sm text-gray-600">Send structured data to your own endpoints</p>
        </div>
        
        <div class="px-6 py-5 space-y-6">
          <div class="flex items-center">
            <%= form.check_box :webhook_notifications, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded", id: "webhook_toggle" %>
            <label for="webhook_toggle" class="ml-2 block text-sm font-medium text-gray-900">Enable webhook notifications</label>
          </div>
          
          <div id="webhook_settings" class="space-y-4" style="<%= 'display: none;' unless @notification_preference.webhook_notifications %>">
            <div>
              <label class="block text-sm font-medium text-gray-900">Webhook URL</label>
              <%= form.url_field :webhook_url, 
                  placeholder: "https://your-app.com/webhooks/formflow", 
                  class: "mt-1 block w-full border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" %>
              <p class="mt-1 text-xs text-gray-500">HTTPS endpoint to receive webhook notifications</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-900 mb-2">Events to send</label>
              <div class="space-y-2">
                <% webhook_events = ['form_abandonment', 'conversion_drop', 'low_traffic', 'new_insight', 'weekly_summary'] %>
                <% webhook_events.each do |event| %>
                  <div class="flex items-center">
                    <%= check_box_tag "notification_preference[webhook_events][]", event, 
                        @notification_preference.webhook_events_array.include?(event),
                        { class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded", 
                          id: "webhook_event_#{event}" } %>
                    <%= hidden_field_tag "notification_preference[webhook_events][]", "", id: nil %>
                    <label for="webhook_event_<%= event %>" class="ml-2 block text-sm text-gray-900">
                      <%= event.humanize %>
                    </label>
                  </div>
                <% end %>
              </div>
            </div>
            
            <div class="pt-4">
              <button type="submit" name="test_webhook" value="true"
                      class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Test Webhook Integration
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Save Button -->
      <div class="flex justify-end">
        <%= form.submit "Save Notification Settings", 
            class: "inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
      </div>
    <% end %>
  </div>
</div>

<!-- JavaScript for toggling sections -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const slackToggle = document.getElementById('slack_integration_toggle');
    const slackSettings = document.getElementById('slack_settings');
    const webhookToggle = document.getElementById('webhook_toggle');
    const webhookSettings = document.getElementById('webhook_settings');

    function toggleSection(checkbox, section) {
      if (checkbox.checked) {
        section.style.display = 'block';
      } else {
        section.style.display = 'none';
      }
    }

    slackToggle.addEventListener('change', function() {
      toggleSection(this, slackSettings);
    });

    webhookToggle.addEventListener('change', function() {
      toggleSection(this, webhookSettings);
    });
  });
</script>