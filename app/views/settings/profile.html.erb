<% content_for :title, "Profile Settings - FormFlow Pro" %>

<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header with animated background -->
    <div class="relative mb-8 p-8 bg-white dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50">
      <div class="absolute inset-0 bg-gradient-to-r from-violet-500/5 to-indigo-500/5 dark:from-violet-400/10 dark:to-indigo-400/10 rounded-2xl"></div>
      <div class="relative">
        <nav class="flex mb-4" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-2">
            <li>
              <%= link_to settings_path, class: "text-gray-500 hover:text-violet-600 dark:text-gray-400 dark:hover:text-violet-400 transition-colors duration-200 font-medium" do %>
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                Settings
              <% end %>
            </li>
            <li class="flex items-center">
              <svg class="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
              <span class="text-gray-900 dark:text-white font-semibold">Profile</span>
            </li>
          </ol>
        </nav>
        <div class="flex items-center space-x-4">
          <div class="w-16 h-16 bg-gradient-to-br from-violet-500 to-indigo-600 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
            <%= current_user.first_name[0].upcase %><%= current_user.last_name[0].upcase %>
          </div>
          <div>
            <h1 class="text-3xl font-bold dark:text-white bg-gradient-to-r from-violet-600 to-indigo-600 bg-clip-text text-transparent">
              Profile Settings
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-300">Customize your personal information and preferences</p>
          </div>
        </div>
      </div>
    </div>

    <%= form_with url: settings_profile_path, method: :patch, local: true, class: "space-y-6" do |form| %>
      <!-- Personal Information Card -->
      <div class="bg-white dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        <div class="bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-900/20 dark:to-indigo-900/20 px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-violet-100 dark:bg-violet-900/50 rounded-xl">
              <svg class="w-5 h-5 text-violet-600 dark:text-violet-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">Personal Information</h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">Update your basic profile details</p>
            </div>
          </div>
        </div>
        
        <div class="p-8">
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
            <div class="space-y-2">
              <label for="first_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                First Name
              </label>
              <input type="text" name="user[first_name]" id="first_name" 
                     value="<%= current_user.first_name %>"
                     class="py-2 px-3 block w-full rounded-xl border-gray-300 dark:border-gray-600 dark:bg-gray-700/50 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all duration-200 text-sm">
            </div>

            <div class="space-y-2">
              <label for="last_name" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                Last Name
              </label>
              <input type="text" name="user[last_name]" id="last_name" 
                     value="<%= current_user.last_name %>"
                     class="py-2 px-3 block w-full rounded-xl border-gray-300 dark:border-gray-600 dark:bg-gray-700/50 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all duration-200 text-sm">
            </div>

            <div class="sm:col-span-2 space-y-2">
              <label for="email" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
                Email Address
              </label>
              <div class="relative">
                <input type="email" name="user[email]" id="email" 
                       value="<%= current_user.email %>"
                       class="py-2 px-3 block w-full rounded-xl border-gray-300 dark:border-gray-600 dark:bg-gray-700/50 dark:text-white shadow-sm focus:border-violet-500 focus:ring-violet-500 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all duration-200 text-sm pl-10">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                  </svg>
                </div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                <svg class="w-4 h-4 inline mr-1 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                This is the email address you use to sign in to your account.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Photo Card -->
      <div class="bg-white dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-indigo-100 dark:bg-indigo-900/50 rounded-xl">
              <svg class="w-5 h-5 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">Profile Photo</h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">Customize your profile appearance</p>
            </div>
          </div>
        </div>
        
        <div class="p-8">
          <div class="flex items-center space-x-8">
            <div class="relative group">
              <div class="w-24 h-24 bg-gradient-to-br from-violet-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-200">
                <%= current_user.first_name[0].upcase %><%= current_user.last_name[0].upcase %>
              </div>
              <div class="absolute inset-0 bg-black/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
            </div>
            <div class="flex-1 space-y-3">
              <h3 class="font-semibold text-gray-900 dark:text-white">Avatar Style</h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Your profile photo is automatically generated from your initials with a beautiful gradient background.
              </p>
              <button type="button" class="inline-flex items-center px-4 py-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700/50 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-violet-300 dark:hover:border-violet-600 transition-all duration-200">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                Upload Custom Photo
                <span class="ml-2 text-xs bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 px-2 py-0.5 rounded">Coming Soon</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Preferences Card -->
      <div class="bg-white dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden">
        <div class="bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 px-8 py-6 border-b border-gray-200/50 dark:border-gray-700/50">
          <div class="flex items-center space-x-3">
            <div class="p-2 bg-green-100 dark:bg-green-900/50 rounded-xl">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div>
              <h2 class="text-xl font-bold text-gray-900 dark:text-white">Preferences</h2>
              <p class="text-sm text-gray-600 dark:text-gray-400">Configure your account preferences</p>
            </div>
          </div>
        </div>
        
        <div class="p-8">
          <div class="max-w-md space-y-2">
            <label for="timezone" class="block text-sm font-semibold text-gray-700 dark:text-gray-300">
              Timezone
            </label>
            <div class="relative">
              <select name="user[timezone]" id="timezone" 
                      class="py-2 px-3 block w-full rounded-xl border-gray-300 dark:border-gray-600 dark:bg-gray-700/50 dark:text-white shadow-sm focus:border-green-500 focus:ring-green-500 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-800 transition-all duration-200 text-sm pl-10">
                <% user_timezone = current_user.timezone || current_user.account&.timezone || 'UTC' %>
                <option value="UTC" <%= 'selected' if user_timezone == 'UTC' %>>🌍 UTC (Coordinated Universal Time)</option>
                <option value="America/New_York" <%= 'selected' if user_timezone == 'America/New_York' %>>🇺🇸 Eastern Time (US & Canada)</option>
                <option value="America/Chicago" <%= 'selected' if user_timezone == 'America/Chicago' %>>🇺🇸 Central Time (US & Canada)</option>
                <option value="America/Denver" <%= 'selected' if user_timezone == 'America/Denver' %>>🇺🇸 Mountain Time (US & Canada)</option>
                <option value="America/Los_Angeles" <%= 'selected' if user_timezone == 'America/Los_Angeles' %>>🇺🇸 Pacific Time (US & Canada)</option>
                <option value="Europe/London" <%= 'selected' if user_timezone == 'Europe/London' %>>🇬🇧 London (GMT/BST)</option>
                <option value="Europe/Paris" <%= 'selected' if user_timezone == 'Europe/Paris' %>>🇫🇷 Paris (CET/CEST)</option>
                <option value="Asia/Tokyo" <%= 'selected' if user_timezone == 'Asia/Tokyo' %>>🇯🇵 Tokyo (JST)</option>
              </select>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <p class="text-xs text-gray-500 dark:text-gray-400 bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
              <svg class="w-4 h-4 inline mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Used for displaying dates and scheduling reports. Current time: <%= Time.current.strftime("%I:%M %p") %>
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center bg-white dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-200/50 dark:border-gray-700/50 px-8 py-6">
        <div class="text-sm text-gray-500 dark:text-gray-400">
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
          Changes are saved securely
        </div>
        <div class="flex space-x-4">
          <%= link_to settings_path, class: "px-6 py-2.5 border-2 border-gray-300 dark:border-gray-600 rounded-xl text-sm font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700/50 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200" do %>
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            Cancel
          <% end %>
          <button type="submit" class="px-6 py-2.5 bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white rounded-xl text-sm font-semibold shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5">
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Save Changes
          </button>
        </div>
      </div>
    <% end %>
  </div>
</div>