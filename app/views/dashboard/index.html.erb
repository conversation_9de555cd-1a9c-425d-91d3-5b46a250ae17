<!-- Top Bar -->
<header class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 sm:px-6 lg:px-8 py-4">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
    <div>
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Welcome back, <%= current_user.first_name %>!</h1>
      <p class="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-1">Here's what's happening with your forms today</p>
    </div>
    
    <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
      <!-- Date Range Selector -->
      <div class="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1 overflow-x-auto">
        <%= link_to dashboard_path(range: "1d"), 
            class: "px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium transition-colors whitespace-nowrap rounded-md #{@date_range == '1d' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600'}" do %>
          Today
        <% end %>
        <%= link_to dashboard_path(range: "7d"), 
            class: "px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium transition-colors whitespace-nowrap rounded-md #{@date_range == '7d' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600'}" do %>
          Last 7 days
        <% end %>
        <%= link_to dashboard_path(range: "30d"), 
            class: "px-2 sm:px-4 py-2 text-xs sm:text-sm font-medium transition-colors whitespace-nowrap rounded-md #{@date_range == '30d' ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm' : 'text-gray-700 dark:text-gray-300 hover:bg-white dark:hover:bg-gray-600'}" do %>
          Last 30 days
        <% end %>
      </div>
      
      <!-- Add Website Button -->
      <%= link_to new_website_path, class: "px-4 sm:px-5 py-2 sm:py-2.5 bg-gradient-to-r from-violet-500 to-indigo-500 text-white rounded-lg font-medium hover:from-violet-600 hover:to-indigo-600 transition-all shadow-lg text-center" do %>
        <span class="flex items-center justify-center gap-2">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          <span class="text-sm sm:text-base">Add Website</span>
        </span>
      <% end %>
    </div>
  </div>
</header>

<!-- Dashboard Content -->
<div class="flex-1 overflow-auto bg-gray-50 dark:bg-gray-900">
  <div class="p-4 sm:p-6 lg:p-8">
          
          <!-- Key Metrics Grid -->
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 sm:gap-6 mb-8">
            <!-- Total Sessions Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                  </svg>
                </div>
                <% growth = @growth_metrics[:growth_percentage] %>
                <span class="text-xs font-medium <%= growth >= 0 ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' : 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' %> px-2 py-1 rounded-full">
                  <%= growth >= 0 ? '+' : '' %><%= growth %>%
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@total_sessions) %></h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Total Sessions</p>
            </div>
            
            <!-- Unique Visitors Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-indigo-100 dark:bg-indigo-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                  </svg>
                </div>
                <span class="text-xs font-medium text-indigo-600 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30 px-2 py-1 rounded-full">
                  Unique
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= number_with_delimiter(@total_visitors) %></h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Unique Visitors</p>
            </div>
            
            <!-- Conversion Rate Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                  </svg>
                </div>
                <% conv_change = @avg_conversion_rate[:change] %>
                <span class="text-xs font-medium <%= conv_change >= 0 ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' : 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' %> px-2 py-1 rounded-full">
                  <%= conv_change >= 0 ? '+' : '' %><%= conv_change %>%
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @avg_conversion_rate[:current] %>%</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Avg Conversion Rate</p>
            </div>
            
            <!-- Total Forms Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
                <span class="text-xs font-medium text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30 px-2 py-1 rounded-full">
                  Active
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @total_forms %></h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Active Forms</p>
            </div>
            
            <!-- Abandonment Rate Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
              <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"></path>
                  </svg>
                </div>
                <% abandon_change = @avg_abandonment_rate[:change] %>
                <span class="text-xs font-medium <%= abandon_change <= 0 ? 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30' : 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30' %> px-2 py-1 rounded-full">
                  <%= abandon_change >= 0 ? '+' : '' %><%= abandon_change %>%
                </span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><%= @avg_abandonment_rate[:current] %>%</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Abandonment Rate</p>
            </div>
          </div>
          
          <!-- Main Content Grid -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6">
            
            <!-- Activity Chart (Left - 2 columns) -->
            <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                  Activity Overview - 
                  <%= case @date_range
                      when "1d" then "Today (Hourly)"
                      when "7d" then "Last 7 Days"
                      when "30d" then "Last 30 Days"
                      end %>
                </h2>
                <div class="flex gap-4 flex-wrap">
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Sessions</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-violet-500 rounded-full"></div>
                    <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Submissions</span>
                  </div>
                </div>
              </div>
              
              <!-- Chart Area -->
              <div class="overflow-x-auto -mx-4 sm:mx-0 px-4 sm:px-0">
                <div class="h-64 flex items-end gap-1 sm:gap-2 min-w-[600px]">
                  <% max_value = [(@weekly_data.map { |d| d[:sessions] }.max || 1), (@weekly_data.map { |d| d[:submissions] }.max || 1)].max %>
                  <% @weekly_data.each do |data| %>
                    <div class="flex-1 flex flex-col items-center gap-2">
                      <div class="w-full flex flex-col justify-end h-48 gap-1 relative">
                        <!-- Sessions Bar -->
                        <div class="bg-blue-500/70 rounded-sm transition-all hover:bg-blue-600" 
                             style="height: <%= data[:sessions] > 0 ? [(data[:sessions].to_f / max_value * 100), 2].max : 2 %>%"
                             title="<%= data[:sessions] %> sessions on <%= data[:label] %>">
                        </div>
                        <!-- Submissions Bar (overlay) -->
                        <div class="bg-violet-500 rounded-sm transition-all hover:bg-violet-600 absolute bottom-0 w-full" 
                             style="height: <%= data[:submissions] > 0 ? [(data[:submissions].to_f / max_value * 100), 2].max : 2 %>%"
                             title="<%= data[:submissions] %> submissions on <%= data[:label] %>">
                        </div>
                      </div>
                      <span class="text-xs text-gray-500 dark:text-gray-400"><%= data[:label] %></span>
                    </div>
                  <% end %>
                </div>
              </div>
              
              <!-- Chart Summary -->
              <div class="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-4 text-center">
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Total Sessions</p>
                  <p class="text-sm font-semibold text-gray-900 dark:text-white"><%= number_with_delimiter(@weekly_data.sum { |d| d[:sessions] }) %></p>
                </div>
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Total Submissions</p>
                  <p class="text-sm font-semibold text-gray-900 dark:text-white"><%= number_with_delimiter(@weekly_data.sum { |d| d[:submissions] }) %></p>
                </div>
                <div>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Peak <%= @date_range == "1d" ? "Hour" : "Day" %></p>
                  <p class="text-sm font-semibold text-gray-900 dark:text-white"><%= @weekly_data.max_by { |d| d[:sessions] }[:label] %></p>
                </div>
              </div>
            </div>
            
            <!-- Right Side Content -->
            <div class="space-y-6">
              
              <!-- Performance Insights -->
              <% if @performance_insights.any? %>
                <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
                  <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Insights</h2>
                  <div class="space-y-3">
                    <% @performance_insights.each do |insight| %>
                      <div class="flex items-start gap-3 p-3 rounded-lg <%= insight[:type] == 'success' ? 'bg-green-50 dark:bg-green-900/20' : insight[:type] == 'warning' ? 'bg-yellow-50 dark:bg-yellow-900/20' : insight[:type] == 'critical' ? 'bg-red-50 dark:bg-red-900/20' : 'bg-blue-50 dark:bg-blue-900/20' %>">
                        <div class="flex-shrink-0 mt-0.5">
                          <% case insight[:type] %>
                          <% when "success" %>
                            <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                          <% when "warning" %>
                            <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                          <% when "critical" %>
                            <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                          <% else %>
                            <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                          <% end %>
                        </div>
                        <div>
                          <p class="text-sm font-medium <%= insight[:type] == 'success' ? 'text-green-800 dark:text-green-200' : insight[:type] == 'warning' ? 'text-yellow-800 dark:text-yellow-200' : insight[:type] == 'critical' ? 'text-red-800 dark:text-red-200' : 'text-blue-800 dark:text-blue-200' %>"><%= insight[:title] %></p>
                          <p class="text-xs <%= insight[:type] == 'success' ? 'text-green-600 dark:text-green-300' : insight[:type] == 'warning' ? 'text-yellow-600 dark:text-yellow-300' : insight[:type] == 'critical' ? 'text-red-600 dark:text-red-300' : 'text-blue-600 dark:text-blue-300' %> mt-1"><%= insight[:message] %></p>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              <% end %>
              
              <!-- Engagement Breakdown -->
              <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Engagement Breakdown</h2>
                
                <!-- Mini Donut Chart -->
                <div class="flex items-center justify-center mb-4">
                  <div class="relative">
                    <svg class="w-32 h-32 transform -rotate-90">
                      <circle cx="64" cy="64" r="56" stroke="currentColor" stroke-width="16" fill="none" class="text-gray-200 dark:text-gray-700"></circle>
                      <circle cx="64" cy="64" r="56" stroke="currentColor" stroke-width="16" fill="none" 
                              stroke-dasharray="<%= @engagement_breakdown[:completed] * 3.52 %> 352"
                              class="text-green-500"></circle>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                      <div class="text-center">
                        <p class="text-2xl font-bold text-gray-900 dark:text-white"><%= @engagement_breakdown[:completed] %>%</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Completed</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Stats -->
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Completed</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @engagement_breakdown[:completed] %>%</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Partial</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @engagement_breakdown[:partial] %>%</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Abandoned</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @engagement_breakdown[:abandoned] %>%</span>
                  </div>
                  <div class="pt-2 border-t border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>Total Sessions:</span>
                      <span><%= number_with_delimiter(@engagement_breakdown[:total_sessions]) %></span>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Conversion Funnel -->
              <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Conversion Funnel</h2>
                <div class="space-y-3">
                  <% @funnel_data.each_with_index do |stage, index| %>
                    <div class="relative">
                      <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><%= stage[:stage] %></span>
                        <span class="text-sm text-gray-500 dark:text-gray-400"><%= number_with_delimiter(stage[:count]) %> (<%= stage[:percentage] %>%)</span>
                      </div>
                      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                        <div class="<%= index == 0 ? 'bg-blue-500' : index == 1 ? 'bg-yellow-500' : 'bg-green-500' %> h-3 rounded-full transition-all duration-300" 
                             style="width: <%= stage[:percentage] %>%"></div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
              
              <!-- Device Analytics -->
              <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
                <h2 class="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">Device Breakdown</h2>
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <svg class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 011 1v8a1 1 0 01-1 1H5a1 1 0 01-1-1V7zM9 9a1 1 0 000 2v2a1 1 0 001 1h1a1 1 0 100-2v-2a1 1 0 00-1-1H9z"></path>
                      </svg>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Mobile</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @device_analytics[:mobile] %></span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Desktop</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @device_analytics[:desktop] %></span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <svg class="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 2a2 2 0 00-2 2v8a2 2 0 002 2h2.22l.123.489.804.804A1 1 0 007 17h6a1 1 0 00-.027-1.707l-.804-.804L12.78 14H15a2 2 0 002-2V4a2 2 0 00-2-2H5zm7 5a1 1 0 10-2 0v2a1 1 0 001 1h2a1 1 0 100-2h-1V7z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="text-sm text-gray-600 dark:text-gray-400">Tablet</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900 dark:text-white"><%= @device_analytics[:tablet] %></span>
                  </div>
                </div>
              </div>
              
              <!-- Critical Insights -->
              <% if @critical_insights.any? %>
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-2xl p-4 sm:p-6">
                  <div class="flex items-center gap-2 mb-4">
                    <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                    </svg>
                    <h2 class="text-lg font-semibold text-red-900 dark:text-red-200">Critical Issues</h2>
                  </div>
                  <div class="space-y-2">
                    <% @critical_insights.first(3).each do |insight| %>
                      <%= link_to form_path(insight.form), class: "block p-3 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" do %>
                        <p class="text-sm font-medium text-gray-900 dark:text-white"><%= insight.title %></p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1"><%= insight.form.name %> • <%= insight.form.website.name %></p>
                      <% end %>
                    <% end %>
                  </div>
                  <%= link_to insights_path, class: "inline-flex items-center text-xs text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 mt-3" do %>
                    View all insights →
                  <% end %>
                </div>
              <% end %>
            </div>
          </div>
          
          <!-- Bottom Section -->
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 mt-8">
            
            <!-- Top Forms -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Top Performing Forms</h2>
                <%= link_to "View all", websites_path, class: "text-sm text-violet-600 dark:text-violet-400 hover:underline" %>
              </div>
              
              <div class="space-y-3">
                <% if @top_forms.any? %>
                  <% @top_forms.each_with_index do |form, index| %>
                    <%= link_to form_path(form), class: "block p-4 bg-gray-50 dark:bg-gray-700/50 rounded-xl hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors" do %>
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                          <div class="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-violet-500 to-indigo-500 text-white rounded-lg font-bold text-sm">
                            <%= index + 1 %>
                          </div>
                          <div>
                            <p class="font-medium text-gray-900 dark:text-white text-sm"><%= truncate(form.name, length: 25) %></p>
                            <p class="text-xs text-gray-500 dark:text-gray-400"><%= truncate(form.website.name, length: 20) %></p>
                          </div>
                        </div>
                        <div class="text-right">
                          <p class="font-semibold text-green-600 dark:text-green-400 text-sm"><%= form.conversion_rate || 0 %>%</p>
                          <p class="text-xs text-gray-500 dark:text-gray-400"><%= form.sessions_count || 0 %> sessions</p>
                        </div>
                      </div>
                    <% end %>
                  <% end %>
                <% else %>
                  <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">No form data available yet</p>
                    <%= link_to new_website_path, class: "inline-flex items-center text-sm text-violet-600 dark:text-violet-400 hover:text-violet-800 dark:hover:text-violet-300 mt-2" do %>
                      Add your first website →
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Activity</h2>
              
              <div class="space-y-3">
                <% if @recent_submissions.any? %>
                  <% @recent_submissions.first(6).each do |submission| %>
                    <%= link_to form_path(submission.form), class: "flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors" do %>
                      <div class="mt-1.5 flex-shrink-0">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm text-gray-900 dark:text-white">
                          New submission on <span class="font-medium"><%= truncate(submission.form.name, length: 20) %></span>
                        </p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <%= truncate(submission.form.website.name, length: 25) %> • <%= time_ago_in_words(submission.submitted_at) %> ago
                        </p>
                      </div>
                      <div class="flex-shrink-0">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      </div>
                    <% end %>
                  <% end %>
                <% else %>
                  <div class="text-center py-8">
                    <svg class="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">No recent activity</p>
                  </div>
                <% end %>
              </div>
            </div>
            
            <!-- Peak Activity Times -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-4 sm:p-6 border border-gray-200 dark:border-gray-700">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Peak Activity Times</h2>
              
              <div class="space-y-4">
                <!-- Peak Hour -->
                <div>
                  <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">Most Active Hour</span>
                    <span class="text-sm text-gray-500 dark:text-gray-400"><%= sprintf("%02d:00", @time_analytics[:peak_hour]) %></span>
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 mb-3">Based on last 7 days</div>
                </div>
                
                <!-- Mini Activity Chart -->
                <div class="grid grid-cols-12 gap-1">
                  <% @time_analytics[:hours_data].each do |hour_data| %>
                    <div class="flex flex-col items-center">
                      <div class="w-2 bg-gray-200 dark:bg-gray-700 rounded-full mb-1"
                           style="height: <%= hour_data[:count] > 0 ? [(hour_data[:count].to_f / @time_analytics[:hours_data].map{|h| h[:count]}.max) * 30, 3].max : 3 %>px">
                        <div class="w-full bg-violet-500 rounded-full"
                             style="height: <%= hour_data[:count] > 0 ? [(hour_data[:count].to_f / @time_analytics[:hours_data].map{|h| h[:count]}.max) * 30, 3].max : 3 %>px"
                             title="<%= hour_data[:count] %> sessions at <%= sprintf("%02d:00", hour_data[:hour]) %>"></div>
                      </div>
                      <% if hour_data[:hour] % 6 == 0 %>
                        <span class="text-xs text-gray-400 dark:text-gray-500"><%= sprintf("%02d", hour_data[:hour]) %></span>
                      <% end %>
                    </div>
                  <% end %>
                </div>
                
                <!-- Quick Stats -->
                <div class="grid grid-cols-2 gap-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                  <div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Morning Peak</p>
                    <p class="text-sm font-semibold text-gray-900 dark:text-white">
                      <%= sprintf("%02d:00", @time_analytics[:hours_data].select { |h| h[:hour].between?(6, 11) }.max_by { |h| h[:count] }[:hour]) %>
                    </p>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Evening Peak</p>
                    <p class="text-sm font-semibold text-gray-900 dark:text-white">
                      <%= sprintf("%02d:00", @time_analytics[:hours_data].select { |h| h[:hour].between?(17, 22) }.max_by { |h| h[:count] }[:hour]) %>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Additional Quick Access Section -->
          <div class="mt-8 bg-gradient-to-r from-violet-50 to-indigo-50 dark:from-violet-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-violet-200 dark:border-violet-800">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Ready to dive deeper?</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">Explore detailed analytics, session replays, and optimization insights.</p>
              </div>
              <div class="flex flex-col sm:flex-row gap-3">
                <%= link_to analytics_path, class: "px-4 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-center" do %>
                  View Analytics
                <% end %>
                <%= link_to insights_path, class: "px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors text-center" do %>
                  View Insights
                <% end %>
                <%= link_to monitoring_dashboard_path, class: "px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-center" do %>
                  Live Monitoring
                <% end %>
              </div>
            </div>
          </div>
          
        </div>
      </div>