class SettingsController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!

  def index
    @account = Current.account
    @user = current_user
    @subscription = @account.subscription
    @api_keys = @account.api_keys.order(created_at: :desc)
    @team_members = @account.users.includes(:roles).order(created_at: :asc)

    # Usage statistics for the current billing period
    @current_period_stats = calculate_current_period_stats
    @billing_history = [] # Would integrate with Stripe billing history

    # Security settings
    @security_settings = {
      two_factor_enabled: false, # Would check actual 2FA status
      login_notifications: true,
      api_access_enabled: true
    }

    # Notification preferences
    @notification_preferences = {
      email_insights: true,
      email_reports: true,
      email_alerts: true,
      slack_integration: false,
      webhook_notifications: false
    }
  end

  def profile
    @user = current_user
  end

  def update_profile
    @user = current_user

    if @user.update(profile_params)
      redirect_to settings_profile_path, notice: "Profile updated successfully."
    else
      render :profile
    end
  end

  def account
    @account = Current.account
  end

  def update_account
    @account = Current.account

    if @account.update(account_params)
      redirect_to settings_account_path, notice: "Account settings updated successfully."
    else
      render :account
    end
  end

  def billing
    @account = Current.account
    @subscription = @account.subscription
    @current_period_stats = calculate_current_period_stats
    @billing_history = [] # Would integrate with Stripe

    # Plan limits and usage
    @plan_limits = get_plan_limits
    @usage_warnings = check_usage_warnings
  end

  def team
    @account = Current.account
    @team_members = @account.users.includes(:roles).order(created_at: :asc)
    @pending_invitations = [] # Would track pending invites
    @available_roles = [ "owner", "admin", "member", "viewer" ]
  end

  def invite_member
    # Team member invitation logic would go here
    redirect_to settings_team_path, notice: "Invitation sent successfully."
  end

  def remove_member
    member = @account.users.find(params[:member_id])

    if member != current_user && can_remove_member?(member)
      member.account_memberships.where(account: @account).destroy_all
      redirect_to settings_team_path, notice: "Team member removed successfully."
    else
      redirect_to settings_team_path, alert: "Unable to remove team member."
    end
  end

  def notifications
    @notification_preference = current_user.notification_preference || current_user.create_notification_preference!
  end

  def update_notifications
    @notification_preference = current_user.notification_preference || current_user.create_notification_preference!
    
    if @notification_preference.update(notification_params)
      # Test Slack webhook if it was just configured
      if params[:test_slack] && @notification_preference.slack_configured?
        test_slack_integration
      end
      
      # Test webhook if it was just configured
      if params[:test_webhook] && @notification_preference.webhook_configured?
        test_webhook_integration
      end
      
      redirect_to settings_notifications_path, notice: "Notification preferences updated successfully."
    else
      render :notifications, status: :unprocessable_entity
    end
  end

  def security
    @user = current_user
    @account = Current.account
    @recent_logins = [] # Would track recent login activity
    @active_sessions = [] # Would track active sessions
  end

  def api_keys
    @account = Current.account
    @api_keys = @account.api_keys.order(created_at: :desc)
  end

  def create_api_key
    @account = Current.account
    
    # Debug logging
    Rails.logger.info "Creating API key with params: #{params.inspect}"
    Rails.logger.info "Name param: #{params[:name]}"
    Rails.logger.info "Permissions param: #{params[:permissions]}"
    
    # Process permissions from checkboxes
    permissions = params[:permissions].present? ? params[:permissions].reject(&:blank?) : ["read"]
    
    api_key = @account.api_keys.build(
      name: params[:name],
      permissions: permissions,
      created_by_id: current_user.id,
      expires_at: params[:expires_at].present? ? Date.parse(params[:expires_at]) : nil
    )
    
    Rails.logger.info "Before validation - Key: #{api_key.key.inspect}"
    Rails.logger.info "Valid?: #{api_key.valid?}"
    Rails.logger.info "Errors before save: #{api_key.errors.full_messages}" unless api_key.valid?
    
    if api_key.save
      # Show the unmasked key only once (stored temporarily in model)
      flash[:api_key] = api_key.unmasked_key
      redirect_to settings_api_keys_path, notice: "API key created successfully. Make sure to copy it now - you won't see it again!"
    else
      Rails.logger.error "Failed to create API key: #{api_key.errors.full_messages.join(', ')}"
      Rails.logger.error "API key attributes: #{api_key.attributes.inspect}"
      redirect_to settings_api_keys_path, alert: "Failed to create API key: #{api_key.errors.full_messages.join(', ')}"
    end
  end

  def revoke_api_key
    @account = Current.account
    api_key = @account.api_keys.find(params[:id])
    
    if api_key.revoke!
      redirect_to settings_api_keys_path, notice: "API key revoked successfully."
    else
      redirect_to settings_api_keys_path, alert: "Failed to revoke API key."
    end
  end

  def integrations
    @account = Current.account
    @integrations = {
      slack: { connected: false, webhook_url: nil },
      zapier: { connected: false, api_key: nil },
      google_analytics: { connected: false, property_id: nil },
      segment: { connected: false, write_key: nil }
    }
  end

  def export_data
    # Data export functionality
    redirect_to settings_path, notice: "Data export initiated. You'll receive an email when it's ready."
  end

  def delete_account
    # Account deletion logic (with proper safeguards)
    if params[:confirmation] == "DELETE"
      # Would implement proper account deletion
      redirect_to root_path, notice: "Account deletion initiated."
    else
      redirect_to settings_account_path, alert: "Please type DELETE to confirm account deletion."
    end
  end

  private

  def profile_params
    params.require(:user).permit(:first_name, :last_name, :email, :timezone)
  end

  def account_params
    params.require(:account).permit(:name, :company_name, :website_url, :timezone)
  end

  def calculate_current_period_stats
    start_date = 1.month.ago.beginning_of_day
    end_date = Time.current.end_of_day

    {
      sessions: FormSession.joins(form: { website: :account })
                          .where(websites: { account_id: @account.id })
                          .where(created_at: start_date..end_date)
                          .count,
      submissions: FormSubmission.joins(form: { website: :account })
                                 .where(websites: { account_id: @account.id })
                                 .where(created_at: start_date..end_date)
                                 .count,
      websites: @account.websites.count,
      forms: @account.forms.count
    }
  end

  def get_plan_limits
    # Would return actual plan limits based on subscription
    {
      sessions: 10000,
      websites: 5,
      forms: 50,
      team_members: 3,
      api_calls: 50000
    }
  end

  def check_usage_warnings
    stats = @current_period_stats
    limits = get_plan_limits
    warnings = []

    if stats[:sessions] > (limits[:sessions] * 0.8)
      warnings << { type: "sessions", usage: stats[:sessions], limit: limits[:sessions] }
    end

    if stats[:websites] > (limits[:websites] * 0.8)
      warnings << { type: "websites", usage: stats[:websites], limit: limits[:websites] }
    end

    warnings
  end

  def can_remove_member?(member)
    # Check if current user has permission to remove this member
    # Would implement proper permission checking
    current_user.account_role(@account) == "owner" && member != current_user
  end

  def notification_params
    params.require(:notification_preference).permit(
      :email_insights, :email_reports, :email_alerts, :email_form_abandonment,
      :email_weekly_summary, :daily_digest, :weekly_digest, :monthly_report,
      :slack_integration, :slack_webhook_url, :slack_channel, :slack_insights,
      :slack_alerts, :slack_reports, :webhook_notifications, :webhook_url,
      :alert_form_abandonment_threshold, :alert_conversion_drop_threshold,
      :alert_low_traffic_threshold,
      webhook_events: []
    )
  end

  def test_slack_integration
    begin
      slack_service = SlackNotificationService.new(
        @notification_preference.slack_webhook_url,
        @notification_preference.slack_channel
      )
      
      # Send a test message
      slack_service.send_to_slack({
        text: "🎉 Slack integration test successful!",
        attachments: [
          {
            color: "good",
            text: "FormFlow Pro notifications are now configured for this channel.",
            footer: "FormFlow Pro",
            ts: Time.current.to_i
          }
        ]
      })
      
      flash[:notice] = "Slack integration test successful! Check your Slack channel."
    rescue StandardError => e
      flash[:alert] = "Slack integration test failed: #{e.message}"
    end
  end

  def test_webhook_integration
    begin
      webhook_service = WebhookNotificationService.new(@notification_preference.webhook_url)
      webhook_service.test_webhook
      
      flash[:notice] = "Webhook test successful! Check your endpoint logs."
    rescue StandardError => e
      flash[:alert] = "Webhook test failed: #{e.message}"
    end
  end
end
