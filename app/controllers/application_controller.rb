class ApplicationController < ActionController::Base
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  before_action :configure_permitted_parameters, if: :devise_controller?
  before_action :set_current_account

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :first_name, :last_name ])
    devise_parameter_sanitizer.permit(:account_update, keys: [ :first_name, :last_name ])
  end

  def set_current_account
    Current.account = current_user&.account
  end

  def require_account!
    unless current_user&.account
      # Create an account for the user if they don't have one
      account_name = "#{current_user.full_name}'s Account"
      
      # Let the Account model handle slug generation
      current_user.create_account!(
        name: account_name,
        billing_email: current_user.email
      )
      set_current_account
    end
  end

  def after_sign_in_path_for(resource)
    dashboard_path
  end
end
