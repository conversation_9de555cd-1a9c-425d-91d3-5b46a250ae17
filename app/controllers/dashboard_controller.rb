class DashboardController < ApplicationController
  before_action :authenticate_user!
  before_action :require_account!

  def index
    @account = Current.account
    @websites = @account.websites.includes(:forms)
    
    # Date range handling
    @date_range = params[:range] || "7d"
    @start_date = case @date_range
                  when "1d" then 1.day.ago
                  when "7d" then 7.days.ago
                  when "30d" then 30.days.ago
                  else 7.days.ago
                  end

    # Core metrics
    @total_forms = @account.forms.count
    @total_sessions = FormSession.joins(form: { website: :account })
                                .where(websites: { account_id: @account.id })
                                .where("form_sessions.created_at > ?", @start_date)
                                .count

    # Recent activity
    @recent_submissions = @account.form_submissions
                                  .includes(form: :website)
                                  .order(submitted_at: :desc)
                                  .limit(10)

    # Performance metrics
    @avg_conversion_rate = calculate_avg_conversion_rate
    @avg_abandonment_rate = calculate_avg_abandonment_rate

    # Critical insights
    @critical_insights = Insight.joins(form: { website: :account })
                               .where(websites: { account_id: @account.id })
                               .where(severity: "critical")
                               .where(resolved_at: nil)
                               .limit(5)

    # Enhanced metrics for comprehensive dashboard
    @total_conversions = @account.form_submissions.where("form_submissions.created_at > ?", @start_date).count
    @active_forms = @account.forms.count
    @total_visitors = FormSession.joins(form: { website: :account })
                                .where(websites: { account_id: @account.id })
                                .where("form_sessions.created_at > ?", @start_date)
                                .distinct
                                .count(:visitor_id)

    # Top performing forms with detailed metrics
    @top_forms = @account.forms
                        .joins("LEFT JOIN form_sessions ON forms.id = form_sessions.form_id")
                        .where("form_sessions.created_at > ? OR form_sessions.created_at IS NULL", @start_date)
                        .group("forms.id")
                        .select("forms.*, 
                                COUNT(form_sessions.id) as sessions_count,
                                COALESCE(forms.conversion_rate, 0) as conversion_rate")
                        .order("conversion_rate DESC, sessions_count DESC")
                        .limit(5)

    # Form engagement breakdown with period comparison
    @engagement_breakdown = calculate_engagement_breakdown
    @previous_engagement = calculate_engagement_breakdown(previous_period: true)

    # Enhanced weekly trends with more data points
    @weekly_data = calculate_enhanced_weekly_trends

    # Recent field events for activity feed
    @recent_events = FieldEvent.joins(form_session: { form: { website: :account } })
                              .where(websites: { account_id: @account.id })
                              .where("field_events.created_at > ?", 24.hours.ago)
                              .order(created_at: :desc)
                              .limit(8)

    # Performance insights
    @performance_insights = calculate_performance_insights

    # Form completion funnel data
    @funnel_data = calculate_funnel_data

    # Device and browser analytics
    @device_analytics = calculate_device_analytics

    # Time-based analytics
    @time_analytics = calculate_time_analytics

    # Growth metrics
    @growth_metrics = calculate_growth_metrics
  end

  def monitoring
    @account = Current.account
    @websites = @account.websites.includes(:forms)
    
    # Real-time monitoring data
    @active_sessions = FormSession.joins(form: { website: :account })
                                 .where(websites: { account_id: @account.id })
                                 .where(status: "active")
                                 .count
    
    # Last 24 hours activity
    @recent_sessions = FormSession.joins(form: { website: :account })
                                 .where(websites: { account_id: @account.id })
                                 .where("form_sessions.created_at > ?", 24.hours.ago)
                                 .order(created_at: :desc)
                                 .limit(20)
    
    # Error rates
    @error_events = FieldEvent.joins(form_session: { form: { website: :account } })
                             .where(websites: { account_id: @account.id })
                             .where(event_type: "error")
                             .where("field_events.created_at > ?", 1.hour.ago)
                             .count
    
    # Performance metrics
    @avg_session_duration = FormSession.joins(form: { website: :account })
                                      .where(websites: { account_id: @account.id })
                                      .where("form_sessions.created_at > ?", 24.hours.ago)
                                      .average(:time_spent)&.round || 0
  end

  private

  def calculate_avg_conversion_rate
    rate = @account.forms.average(:conversion_rate)&.round(2) || 0
    previous_rate = @account.forms.joins(:form_sessions)
                           .where("form_sessions.created_at BETWEEN ? AND ?", 
                                  @start_date - (@start_date - 30.days.ago), @start_date)
                           .average(:conversion_rate)&.round(2) || 0
    
    change = previous_rate > 0 ? ((rate - previous_rate) / previous_rate * 100).round(1) : 0
    { current: rate, change: change }
  end

  def calculate_avg_abandonment_rate
    rate = @account.forms.average(:abandonment_rate)&.round(2) || 0
    previous_rate = @account.forms.joins(:form_sessions)
                           .where("form_sessions.created_at BETWEEN ? AND ?", 
                                  @start_date - (@start_date - 30.days.ago), @start_date)
                           .average(:abandonment_rate)&.round(2) || 0
    
    change = previous_rate > 0 ? ((rate - previous_rate) / previous_rate * 100).round(1) : 0
    { current: rate, change: change }
  end

  def calculate_engagement_breakdown(previous_period: false)
    date_filter = if previous_period
                    previous_start = @start_date - (@start_date - 30.days.ago)
                    previous_end = @start_date
                    "form_sessions.created_at BETWEEN ? AND ?"
                  else
                    "form_sessions.created_at > ?"
                  end
    
    date_params = previous_period ? [@start_date - (@start_date - 30.days.ago), @start_date] : [@start_date]

    total_sessions = FormSession.joins(form: { website: :account })
                               .where(websites: { account_id: @account.id })
                               .where(date_filter, *date_params)
                               .count
    return { completed: 0, partial: 0, abandoned: 0 } if total_sessions == 0

    completed = FormSession.joins(form: { website: :account })
                          .where(websites: { account_id: @account.id })
                          .where(date_filter, *date_params)
                          .where(status: "completed")
                          .count
                          
    abandoned = FormSession.joins(form: { website: :account })
                          .where(websites: { account_id: @account.id })
                          .where(date_filter, *date_params)
                          .where(status: "abandoned")
                          .count
                          
    partial = total_sessions - completed - abandoned

    {
      completed: ((completed.to_f / total_sessions) * 100).round,
      partial: ((partial.to_f / total_sessions) * 100).round,
      abandoned: ((abandoned.to_f / total_sessions) * 100).round,
      total_sessions: total_sessions
    }
  end

  def calculate_enhanced_weekly_trends
    days_count = case @date_range
                when "1d" then 24 # Hours for today
                when "7d" then 7  # Days for week
                when "30d" then 30 # Days for month
                else 7
                end

    if @date_range == "1d"
      # Hourly data for today
      (0..23).map do |hour|
        hour_start = Date.current.beginning_of_day + hour.hours
        hour_end = hour_start + 1.hour
        
        sessions = FormSession.joins(form: { website: :account })
                             .where(websites: { account_id: @account.id })
                             .where("form_sessions.created_at": hour_start..hour_end)
                             .count
                             
        submissions = @account.form_submissions.where("form_submissions.created_at": hour_start..hour_end).count
        
        {
          label: hour_start.strftime("%H:00"),
          sessions: sessions,
          submissions: submissions,
          conversions: submissions # For forms, submissions are conversions
        }
      end
    else
      # Daily data
      days_count.downto(0).map do |days_ago|
        date = days_ago.days.ago.to_date
        
        sessions = FormSession.joins(form: { website: :account })
                             .where(websites: { account_id: @account.id })
                             .where("form_sessions.created_at": date.all_day)
                             .count
                             
        submissions = @account.form_submissions.where("form_submissions.created_at": date.all_day).count
        
        {
          label: date.strftime(@date_range == "30d" ? "%m/%d" : "%a"),
          sessions: sessions,
          submissions: submissions,
          conversions: submissions
        }
      end
    end
  end

  def calculate_performance_insights
    insights = []
    
    # Conversion rate insight
    avg_conversion = @avg_conversion_rate[:current]
    if avg_conversion > 0
      if avg_conversion > 15
        insights << { type: "success", title: "Excellent conversion rate", message: "Your forms are performing above industry average" }
      elsif avg_conversion < 5
        insights << { type: "warning", title: "Low conversion rate", message: "Consider optimizing your forms for better performance" }
      end
    end

    # Abandonment insight
    avg_abandonment = @avg_abandonment_rate[:current]
    if avg_abandonment > 70
      insights << { type: "critical", title: "High abandonment rate", message: "Most users are leaving your forms incomplete" }
    end

    # Activity insight
    if @total_sessions < 10 && Date.current.wday.between?(1, 5) # Weekday
      insights << { type: "info", title: "Low activity", message: "Consider promoting your forms for more engagement" }
    end

    insights
  end

  def calculate_funnel_data
    # Simplified funnel: Started -> Interacted -> Completed
    started = FormSession.joins(form: { website: :account })
                        .where(websites: { account_id: @account.id })
                        .where("form_sessions.created_at > ?", @start_date)
                        .count

    interacted = FormSession.joins(form: { website: :account })
                           .where(websites: { account_id: @account.id })
                           .where("form_sessions.created_at > ?", @start_date)
                           .joins(:field_events)
                           .distinct
                           .count

    completed = FormSession.joins(form: { website: :account })
                          .where(websites: { account_id: @account.id })
                          .where("form_sessions.created_at > ?", @start_date)
                          .where(status: "completed")
                          .count

    [
      { stage: "Started", count: started, percentage: 100 },
      { stage: "Interacted", count: interacted, percentage: started > 0 ? (interacted.to_f / started * 100).round : 0 },
      { stage: "Completed", count: completed, percentage: started > 0 ? (completed.to_f / started * 100).round : 0 }
    ]
  end

  def calculate_device_analytics
    # Simplified device tracking - would be enhanced with actual user agent parsing
    sessions = FormSession.joins(form: { website: :account })
                         .where(websites: { account_id: @account.id })
                         .where("form_sessions.created_at > ?", @start_date)

    total = sessions.count
    return { mobile: 0, desktop: 0, tablet: 0 } if total == 0

    # Mock data - in real implementation, parse user_agent
    {
      mobile: (total * 0.6).round,
      desktop: (total * 0.35).round,
      tablet: (total * 0.05).round
    }
  end

  def calculate_time_analytics
    # Peak hours analysis
    hours_data = (0..23).map do |hour|
      count = FormSession.joins(form: { website: :account })
                        .where(websites: { account_id: @account.id })
                        .where("form_sessions.created_at > ?", 7.days.ago)
                        .where("EXTRACT(hour FROM form_sessions.created_at) = ?", hour)
                        .count
      { hour: hour, count: count }
    end

    peak_hour = hours_data.max_by { |h| h[:count] }[:hour]
    
    {
      peak_hour: peak_hour,
      hours_data: hours_data
    }
  end

  def calculate_growth_metrics
    current_period_sessions = @total_sessions
    previous_period_sessions = FormSession.joins(form: { website: :account })
                                         .where(websites: { account_id: @account.id })
                                         .where("form_sessions.created_at BETWEEN ? AND ?", 
                                               @start_date - (@start_date - 30.days.ago), @start_date)
                                         .count

    growth = if previous_period_sessions > 0
               ((current_period_sessions - previous_period_sessions).to_f / previous_period_sessions * 100).round(1)
             else
               0
             end

    {
      current: current_period_sessions,
      previous: previous_period_sessions,
      growth_percentage: growth
    }
  end

  def calculate_weekly_trends
    7.downto(0).map do |days_ago|
      date = days_ago.days.ago.to_date
      {
        date: date.strftime("%a"),
        submissions: @account.form_submissions.where("form_submissions.created_at": date.all_day).count,
        conversions: @account.form_submissions.where("form_submissions.created_at": date.all_day).count
      }
    end
  end
end
