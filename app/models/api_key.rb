class ApiKey < ApplicationRecord
  belongs_to :account
  belongs_to :created_by, class_name: 'User', foreign_key: 'created_by_id', optional: true

  # Callbacks (must come before validations that depend on them)
  before_validation :generate_key, on: :create
  after_create :mask_key_for_storage

  # Validations
  validates :name, presence: true, length: { maximum: 100 }
  validates :key, presence: true, uniqueness: true
  validates :permissions, presence: true

  # Scopes
  scope :active, -> { where(active: true) }
  scope :expired, -> { where("expires_at < ?", Time.current) }
  scope :valid, -> { active.where("expires_at IS NULL OR expires_at > ?", Time.current) }

  # Store the unmasked key temporarily for display after creation
  attr_accessor :unmasked_key

  # Permissions
  AVAILABLE_PERMISSIONS = {
    'read' => 'Read analytics data and view reports',
    'write' => 'Create and modify tracking data',
    'delete' => 'Delete data and configurations',
    'admin' => 'Full administrative access'
  }.freeze

  def self.authenticate(key_string)
    # Find the key by matching the prefix
    prefix = key_string[0..7]
    api_key = find_by("key LIKE ?", "#{prefix}%")
    
    return nil unless api_key
    return nil unless api_key.valid_for_use?
    
    # Verify the full key matches
    return nil unless api_key.verify_key(key_string)
    
    api_key.touch(:last_used_at)
    api_key
  end

  def valid_for_use?
    active? && !expired?
  end

  def expired?
    expires_at.present? && expires_at < Time.current
  end

  def has_permission?(permission)
    permissions.include?(permission.to_s) || permissions.include?('admin')
  end

  def display_key
    return unmasked_key if unmasked_key.present?
    
    # Show first 8 and last 4 characters
    "#{key[0..7]}#{'*' * 24}#{key[-4..]}" if key.present?
  end

  def verify_key(key_string)
    # In production, you'd want to use bcrypt or similar for secure comparison
    ActiveSupport::SecurityUtils.secure_compare(key_string, full_key)
  end

  def revoke!
    update!(active: false, revoked_at: Time.current)
  end

  def usage_stats
    {
      total_requests: request_count || 0,
      last_30_days: recent_request_count || 0,
      last_used: last_used_at,
      rate_limit_hits: rate_limit_hits || 0
    }
  end

  private

  def generate_key
    return if self.key.present? # Don't regenerate if key already exists
    
    # Generate a secure random key with a prefix for easy identification
    prefix = "ffp_#{Rails.env.production? ? 'live' : 'test'}"
    random_part = SecureRandom.hex(20)
    self.key = "#{prefix}_#{random_part}"
    self.unmasked_key = self.key
    
    # Store the full key securely (in production, encrypt this)
    self.full_key = self.key
  end

  def mask_key_for_storage
    # After creation, mask the key for storage (keep only partial info)
    # In production, you'd encrypt the full_key field
    masked = "#{key[0..7]}#{'*' * (key.length - 12)}#{key[-4..]}"
    update_column(:key, masked)
  end

  def full_key
    # In production, decrypt this field
    self[:full_key]
  end
end