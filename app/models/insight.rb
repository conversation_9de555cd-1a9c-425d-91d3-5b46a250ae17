# frozen_string_literal: true

# Insight represents an automatically generated insight about form performance
class Insight < ApplicationRecord
  # Disable STI since we're using type as a regular column
  self.inheritance_column = :_type_disabled

  # Associations
  belongs_to :form

  # Validations
  validates :insight_type, presence: true
  validates :severity, presence: true, inclusion: { in: %w[critical high medium low] }
  validates :description, presence: true

  # Scopes
  scope :unresolved, -> { where(resolved_at: nil) }
  scope :resolved, -> { where.not(resolved_at: nil) }
  scope :critical, -> { where(severity: "critical") }
  scope :high, -> { where(severity: "high") }
  scope :medium, -> { where(severity: "medium") }
  scope :low, -> { where(severity: "low") }
  scope :recent, -> { order(created_at: :desc) }
  scope :not_notified, -> { where(notified_at: nil) }

  # Callbacks
  before_validation :set_detected_at, on: :create

  # Instance methods
  def resolved?
    resolved_at.present?
  end

  def resolve!
    update!(resolved_at: Time.current)
  end

  def age_in_days
    ((Time.current - detected_at) / 1.day).round
  end

  def auto_resolvable?
    insight_type.in?(%w[field_problem high_abandonment slow_field])
  end

  def impact_level
    case impact_score
    when 80..100
      "very_high"
    when 60..79
      "high"
    when 40..59
      "medium"
    when 20..39
      "low"
    else
      "very_low"
    end
  end

  private

  def set_detected_at
    self.detected_at ||= Time.current
  end

end
