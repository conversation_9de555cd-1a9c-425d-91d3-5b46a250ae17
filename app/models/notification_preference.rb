class NotificationPreference < ApplicationRecord
  belongs_to :user
  
  # Email notification preferences
  attribute :email_insights, :boolean, default: true
  attribute :email_reports, :boolean, default: true
  attribute :email_alerts, :boolean, default: true
  attribute :email_form_abandonment, :boolean, default: true
  attribute :email_weekly_summary, :boolean, default: true
  
  # Slack integration settings
  attribute :slack_integration, :boolean, default: false
  attribute :slack_webhook_url, :string
  attribute :slack_channel, :string, default: '#general'
  attribute :slack_insights, :boolean, default: false
  attribute :slack_alerts, :boolean, default: false
  attribute :slack_reports, :boolean, default: false
  
  # Webhook notifications
  attribute :webhook_notifications, :boolean, default: false
  attribute :webhook_url, :string
  attribute :webhook_events, :string, default: '[]'
  
  # Frequency settings
  attribute :daily_digest, :boolean, default: false
  attribute :weekly_digest, :boolean, default: true
  attribute :monthly_report, :boolean, default: true
  
  # Alert thresholds
  attribute :alert_form_abandonment_threshold, :integer, default: 70 # percentage
  attribute :alert_conversion_drop_threshold, :integer, default: 20 # percentage drop
  attribute :alert_low_traffic_threshold, :integer, default: 10 # sessions per day
  
  validates :user_id, presence: true, uniqueness: true
  validates :slack_webhook_url, format: { with: URI::DEFAULT_PARSER.make_regexp(['http', 'https']) }, allow_blank: true
  validates :webhook_url, format: { with: URI::DEFAULT_PARSER.make_regexp(['http', 'https']) }, allow_blank: true
  validates :alert_form_abandonment_threshold, inclusion: { in: 1..100 }
  validates :alert_conversion_drop_threshold, inclusion: { in: 1..100 }
  validates :alert_low_traffic_threshold, numericality: { greater_than: 0 }
  
  def webhook_events_array
    JSON.parse(webhook_events || '[]')
  rescue JSON::ParserError
    []
  end
  
  def webhook_events_array=(events)
    self.webhook_events = events.to_json
  end
  
  def slack_configured?
    slack_integration && slack_webhook_url.present?
  end
  
  def webhook_configured?
    webhook_notifications && webhook_url.present?
  end
end