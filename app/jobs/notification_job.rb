class NotificationJob < ApplicationJob
  queue_as :notifications
  
  retry_on StandardError, wait: :exponentially_longer, attempts: 3
  discard_on ActiveRecord::RecordNotFound

  def perform(notification_type, user_id, *args)
    user = User.find(user_id)
    notification_preference = user.notification_preference
    
    return unless notification_preference
    
    case notification_type.to_s
    when 'daily_digest'
      send_daily_digest(user, notification_preference)
    when 'weekly_summary'
      send_weekly_summary(user, notification_preference)
    when 'monthly_report'
      send_monthly_report(user, notification_preference)
    when 'form_abandonment_alert'
      send_form_abandonment_alert(user, notification_preference, *args)
    when 'conversion_drop_alert'
      send_conversion_drop_alert(user, notification_preference, *args)
    when 'low_traffic_alert'
      send_low_traffic_alert(user, notification_preference, *args)
    when 'new_insight'
      send_new_insight(user, notification_preference, *args)
    else
      Rails.logger.warn "Unknown notification type: #{notification_type}"
    end
  end

  private

  def send_daily_digest(user, preferences)
    return unless preferences.daily_digest
    
    # Send email notification
    if preferences.email_reports
      NotificationMailer.daily_digest(user).deliver_now
    end
    
    # Send Slack notification
    if preferences.slack_configured? && preferences.slack_reports
      metrics = calculate_daily_metrics_for_slack(user.account)
      slack_service = SlackNotificationService.new(
        preferences.slack_webhook_url,
        preferences.slack_channel
      )
      slack_service.send_daily_summary(user.account, metrics)
    end
  end

  def send_weekly_summary(user, preferences)
    return unless preferences.weekly_digest
    
    if preferences.email_reports
      NotificationMailer.weekly_summary(user).deliver_now
    end
  end

  def send_monthly_report(user, preferences)
    return unless preferences.monthly_report
    
    if preferences.email_reports
      NotificationMailer.monthly_report(user).deliver_now
    end
  end

  def send_form_abandonment_alert(user, preferences, form_id, abandonment_rate)
    form = Form.find(form_id)
    
    # Check if alert is needed based on threshold
    threshold = preferences.alert_form_abandonment_threshold || 70
    return unless abandonment_rate >= threshold
    
    # Send email alert
    if preferences.email_alerts
      NotificationMailer.form_abandonment_alert(user, form, abandonment_rate).deliver_now
    end
    
    # Send Slack alert
    if preferences.slack_configured? && preferences.slack_alerts
      slack_service = SlackNotificationService.new(
        preferences.slack_webhook_url,
        preferences.slack_channel
      )
      slack_service.send_form_abandonment_alert(form, abandonment_rate)
    end
    
    # Send webhook notification
    if preferences.webhook_configured? && preferences.webhook_events_array.include?('form_abandonment')
      WebhookNotificationService.new(preferences.webhook_url).send_form_abandonment_alert(form, abandonment_rate)
    end
  end

  def send_conversion_drop_alert(user, preferences, form_id, current_rate, previous_rate)
    form = Form.find(form_id)
    drop_percentage = ((previous_rate - current_rate) / previous_rate * 100).round(1)
    
    # Check if alert is needed based on threshold
    threshold = preferences.alert_conversion_drop_threshold || 20
    return unless drop_percentage >= threshold
    
    # Send email alert
    if preferences.email_alerts
      NotificationMailer.conversion_drop_alert(user, form, current_rate, previous_rate).deliver_now
    end
    
    # Send Slack alert
    if preferences.slack_configured? && preferences.slack_alerts
      slack_service = SlackNotificationService.new(
        preferences.slack_webhook_url,
        preferences.slack_channel
      )
      slack_service.send_conversion_drop_alert(form, current_rate, previous_rate)
    end
  end

  def send_low_traffic_alert(user, preferences, form_id, session_count)
    form = Form.find(form_id)
    
    # Check if alert is needed based on threshold
    threshold = preferences.alert_low_traffic_threshold || 10
    return unless session_count < threshold
    
    # Send email alert
    if preferences.email_alerts
      NotificationMailer.low_traffic_alert(user, form, session_count).deliver_now
    end
    
    # Send Slack alert
    if preferences.slack_configured? && preferences.slack_alerts
      slack_service = SlackNotificationService.new(
        preferences.slack_webhook_url,
        preferences.slack_channel
      )
      slack_service.send_low_traffic_alert(form, session_count, threshold)
    end
  end

  def send_new_insight(user, preferences, insight_id)
    insight = Insight.find(insight_id)
    
    # Send email notification
    if preferences.email_insights
      NotificationMailer.new_insight(user, insight).deliver_now
    end
    
    # Send Slack notification
    if preferences.slack_configured? && preferences.slack_insights
      slack_service = SlackNotificationService.new(
        preferences.slack_webhook_url,
        preferences.slack_channel
      )
      slack_service.send_new_insight(insight)
    end
  end

  def calculate_daily_metrics_for_slack(account)
    yesterday = 1.day.ago.all_day
    today = Date.current.all_day
    
    {
      today_sessions: account.form_sessions.where(created_at: today).count,
      yesterday_sessions: account.form_sessions.where(created_at: yesterday).count,
      today_conversions: account.form_submissions.where("form_submissions.created_at": today).count,
      yesterday_conversions: account.form_submissions.where("form_submissions.created_at": yesterday).count,
      forms_count: account.forms.count,
      active_forms: account.forms.joins(:form_sessions).where(form_sessions: { created_at: today }).distinct.count
    }
  end
end