class NotificationMailer < ApplicationMailer
  default from: Rails.application.credentials.dig(:email, :from_address) || '<EMAIL>'

  # Daily digest email with key metrics
  def daily_digest(user)
    @user = user
    @account = user.account
    return unless @account
    
    @date = Date.current
    @metrics = calculate_daily_metrics
    @insights = @account.insights.where(created_at: @date.all_day).limit(3)
    
    mail(
      to: user.email,
      subject: "FormFlow Pro Daily Digest - #{@date.strftime('%B %d, %Y')}"
    )
  end

  # Weekly summary email
  def weekly_summary(user)
    @user = user
    @account = user.account
    return unless @account
    
    @week_start = 1.week.ago.beginning_of_week
    @week_end = @week_start.end_of_week
    @metrics = calculate_weekly_metrics
    @top_forms = find_top_performing_forms
    @insights = @account.insights.where(created_at: @week_start..@week_end)
    
    mail(
      to: user.email,
      subject: "FormFlow Pro Weekly Summary - Week of #{@week_start.strftime('%B %d, %Y')}"
    )
  end

  # Monthly report email
  def monthly_report(user)
    @user = user
    @account = user.account
    return unless @account
    
    @month_start = 1.month.ago.beginning_of_month
    @month_end = @month_start.end_of_month
    @metrics = calculate_monthly_metrics
    @trends = calculate_monthly_trends
    @recommendations = generate_monthly_recommendations
    
    mail(
      to: user.email,
      subject: "FormFlow Pro Monthly Report - #{@month_start.strftime('%B %Y')}"
    )
  end

  # Form abandonment alert
  def form_abandonment_alert(user, form, abandonment_rate)
    @user = user
    @form = form
    @account = user.account
    @abandonment_rate = abandonment_rate
    @threshold = user.notification_preference&.alert_form_abandonment_threshold || 70
    @recent_sessions = @form.form_sessions.where(status: 'abandoned').recent.limit(5)
    
    mail(
      to: user.email,
      subject: "🚨 High Form Abandonment Alert: #{@form.name}"
    )
  end

  # Conversion rate drop alert
  def conversion_drop_alert(user, form, current_rate, previous_rate)
    @user = user
    @form = form
    @account = user.account
    @current_rate = current_rate
    @previous_rate = previous_rate
    @drop_percentage = ((previous_rate - current_rate) / previous_rate * 100).round(1)
    
    mail(
      to: user.email,
      subject: "📉 Conversion Rate Drop Alert: #{@form.name}"
    )
  end

  # Low traffic alert
  def low_traffic_alert(user, form, session_count)
    @user = user
    @form = form
    @account = user.account
    @session_count = session_count
    @threshold = user.notification_preference&.alert_low_traffic_threshold || 10
    
    mail(
      to: user.email,
      subject: "📊 Low Traffic Alert: #{@form.name}"
    )
  end

  # New insight notification
  def new_insight(user, insight)
    @user = user
    @insight = insight
    @form = insight.form
    @account = user.account
    
    mail(
      to: user.email,
      subject: "💡 New Insight: #{@form.name}"
    )
  end

  private

  def calculate_daily_metrics
    yesterday = 1.day.ago.all_day
    today = Date.current.all_day
    
    {
      today_sessions: @account.form_sessions.where(created_at: today).count,
      yesterday_sessions: @account.form_sessions.where(created_at: yesterday).count,
      today_conversions: @account.form_submissions.where(created_at: today).count,
      yesterday_conversions: @account.form_submissions.where(created_at: yesterday).count,
      forms_count: @account.forms.count,
      active_forms: @account.forms.joins(:form_sessions).where(form_sessions: { created_at: today }).distinct.count
    }
  end

  def calculate_weekly_metrics
    {
      total_sessions: @account.form_sessions.where(created_at: @week_start..@week_end).count,
      total_conversions: @account.form_submissions.where("form_submissions.created_at": @week_start..@week_end).count,
      avg_session_duration: @account.form_sessions.where(created_at: @week_start..@week_end).average(:time_spent)&.to_i || 0,
      unique_visitors: @account.form_sessions.where(created_at: @week_start..@week_end).distinct.count(:visitor_id)
    }
  end

  def calculate_monthly_metrics
    {
      total_sessions: @account.form_sessions.where(created_at: @month_start..@month_end).count,
      total_conversions: @account.form_submissions.where("form_submissions.created_at": @month_start..@month_end).count,
      avg_conversion_rate: @account.forms.average(:conversion_rate)&.round(2) || 0,
      avg_abandonment_rate: @account.forms.average(:abandonment_rate)&.round(2) || 0
    }
  end

  def find_top_performing_forms
    @account.forms
            .joins(:form_sessions)
            .where(form_sessions: { created_at: @week_start..@week_end })
            .group('forms.id')
            .order('COUNT(form_sessions.id) DESC')
            .limit(3)
  end

  def calculate_monthly_trends
    # Compare with previous month
    prev_month_start = @month_start - 1.month
    prev_month_end = prev_month_start.end_of_month
    
    current_sessions = @account.form_sessions.where(created_at: @month_start..@month_end).count
    previous_sessions = @account.form_sessions.where(created_at: prev_month_start..prev_month_end).count
    
    {
      sessions_growth: calculate_percentage_change(previous_sessions, current_sessions),
      conversions_growth: calculate_percentage_change(
        @account.form_submissions.where("form_submissions.created_at": prev_month_start..prev_month_end).count,
        @account.form_submissions.where("form_submissions.created_at": @month_start..@month_end).count
      )
    }
  end

  def generate_monthly_recommendations
    recommendations = []
    
    # Low conversion rate forms
    low_conversion_forms = @account.forms.where('conversion_rate < ?', 5)
    if low_conversion_forms.any?
      recommendations << {
        type: 'improvement',
        title: 'Optimize Low-Converting Forms',
        description: "#{low_conversion_forms.count} forms have conversion rates below 5%",
        action: 'Review form design and user experience'
      }
    end
    
    # High abandonment forms
    high_abandonment_forms = @account.forms.where('abandonment_rate > ?', 70)
    if high_abandonment_forms.any?
      recommendations << {
        type: 'urgent',
        title: 'Address High Form Abandonment',
        description: "#{high_abandonment_forms.count} forms have abandonment rates above 70%",
        action: 'Simplify forms and reduce friction points'
      }
    end
    
    recommendations
  end

  def calculate_percentage_change(previous, current)
    return 0 if previous == 0
    ((current - previous).to_f / previous * 100).round(1)
  end
end