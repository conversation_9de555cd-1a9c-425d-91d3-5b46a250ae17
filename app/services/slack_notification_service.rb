class SlackNotificationService
  include HTT<PERSON><PERSON>y
  
  def initialize(webhook_url, channel = nil)
    @webhook_url = webhook_url
    @channel = channel || '#general'
  end

  # Send form abandonment alert to Slack
  def send_form_abandonment_alert(form, abandonment_rate)
    message = {
      text: "🚨 *High Form Abandonment Alert*",
      attachments: [
        {
          color: "danger",
          title: form.name,
          title_link: form_url(form),
          fields: [
            {
              title: "Abandonment Rate",
              value: "#{abandonment_rate.round(1)}%",
              short: true
            },
            {
              title: "Website",
              value: form.website.domain,
              short: true
            },
            {
              title: "Recent Sessions",
              value: form.form_sessions.where(status: 'abandoned').recent.count.to_s,
              short: true
            }
          ],
          footer: "FormFlow Pro",
          ts: Time.current.to_i
        }
      ]
    }

    send_to_slack(message)
  end

  # Send conversion rate drop alert to Slack
  def send_conversion_drop_alert(form, current_rate, previous_rate)
    drop_percentage = ((previous_rate - current_rate) / previous_rate * 100).round(1)
    
    message = {
      text: "📉 *Conversion Rate Drop Alert*",
      attachments: [
        {
          color: "warning",
          title: form.name,
          title_link: form_url(form),
          fields: [
            {
              title: "Current Rate",
              value: "#{current_rate.round(1)}%",
              short: true
            },
            {
              title: "Previous Rate",
              value: "#{previous_rate.round(1)}%",
              short: true
            },
            {
              title: "Drop",
              value: "#{drop_percentage}%",
              short: true
            }
          ],
          footer: "FormFlow Pro",
          ts: Time.current.to_i
        }
      ]
    }

    send_to_slack(message)
  end

  # Send new insight notification to Slack
  def send_new_insight(insight)
    severity_emoji = case insight.severity
                     when 'critical' then '🔴'
                     when 'high' then '🟠'
                     when 'medium' then '🟡'
                     else '🟢'
                     end

    message = {
      text: "#{severity_emoji} *New Insight Generated*",
      attachments: [
        {
          color: insight.severity == 'critical' ? 'danger' : 'good',
          title: insight.form.name,
          title_link: form_url(insight.form),
          text: insight.description,
          fields: [
            {
              title: "Severity",
              value: insight.severity.capitalize,
              short: true
            },
            {
              title: "Category",
              value: insight.insight_type.humanize,
              short: true
            }
          ],
          footer: "FormFlow Pro",
          ts: Time.current.to_i
        }
      ]
    }

    send_to_slack(message)
  end

  # Send daily summary to Slack
  def send_daily_summary(account, metrics)
    sessions_change = calculate_percentage_change(metrics[:yesterday_sessions], metrics[:today_sessions])
    conversions_change = calculate_percentage_change(metrics[:yesterday_conversions], metrics[:today_conversions])

    message = {
      text: "📊 *Daily FormFlow Summary*",
      attachments: [
        {
          color: "good",
          title: "Today's Performance",
          fields: [
            {
              title: "Sessions",
              value: "#{metrics[:today_sessions]} (#{format_percentage_change(sessions_change)})",
              short: true
            },
            {
              title: "Conversions",
              value: "#{metrics[:today_conversions]} (#{format_percentage_change(conversions_change)})",
              short: true
            },
            {
              title: "Active Forms",
              value: "#{metrics[:active_forms]} / #{metrics[:forms_count]}",
              short: true
            }
          ],
          footer: "FormFlow Pro",
          ts: Time.current.to_i
        }
      ]
    }

    send_to_slack(message)
  end

  # Send low traffic alert to Slack
  def send_low_traffic_alert(form, session_count, threshold)
    message = {
      text: "📊 *Low Traffic Alert*",
      attachments: [
        {
          color: "warning",
          title: form.name,
          title_link: form_url(form),
          text: "Form traffic is below the threshold",
          fields: [
            {
              title: "Today's Sessions",
              value: session_count.to_s,
              short: true
            },
            {
              title: "Threshold",
              value: threshold.to_s,
              short: true
            },
            {
              title: "Website",
              value: form.website.domain,
              short: true
            }
          ],
          footer: "FormFlow Pro",
          ts: Time.current.to_i
        }
      ]
    }

    send_to_slack(message)
  end

  def send_to_slack(message)
    send_notification(message)
  end

  private

  def send_notification(message)
    message[:channel] = @channel if @channel
    
    response = HTTParty.post(
      @webhook_url,
      body: message.to_json,
      headers: {
        'Content-Type' => 'application/json'
      }
    )

    unless response.success?
      Rails.logger.error "Failed to send Slack notification: #{response.code} - #{response.body}"
      raise "Slack notification failed: #{response.message}"
    end

    response
  rescue HTTParty::Error, StandardError => e
    Rails.logger.error "Slack notification error: #{e.message}"
    raise e
  end

  def form_url(form)
    # This would be the actual URL to the form analytics page
    Rails.application.routes.url_helpers.dashboard_url(host: 'app.formflowpro.com')
  end

  def calculate_percentage_change(previous, current)
    return 0 if previous == 0
    ((current - previous).to_f / previous * 100).round(1)
  end

  def format_percentage_change(percentage)
    if percentage > 0
      "+#{percentage}%"
    elsif percentage < 0
      "#{percentage}%"
    else
      "0%"
    end
  end
end