class NotificationSchedulerService
  def self.schedule_daily_digests
    User.joins(:notification_preference)
        .where(notification_preferences: { daily_digest: true })
        .find_each do |user|
      NotificationJob.perform_later('daily_digest', user.id)
    end
  end

  def self.schedule_weekly_summaries
    User.joins(:notification_preference)
        .where(notification_preferences: { weekly_digest: true })
        .find_each do |user|
      NotificationJob.perform_later('weekly_summary', user.id)
    end
  end

  def self.schedule_monthly_reports
    User.joins(:notification_preference)
        .where(notification_preferences: { monthly_report: true })
        .find_each do |user|
      NotificationJob.perform_later('monthly_report', user.id)
    end
  end

  def self.check_form_abandonment_alerts
    Form.includes(website: :account).find_each do |form|
      # Calculate abandonment rate for the last 24 hours
      recent_sessions = form.form_sessions.where('created_at > ?', 24.hours.ago)
      next if recent_sessions.count < 10 # Need minimum sessions for meaningful data
      
      abandoned_count = recent_sessions.where(status: 'abandoned').count
      abandonment_rate = (abandoned_count.to_f / recent_sessions.count * 100).round(1)
      
      # Get users who should receive alerts for this account
      form.website.account.users.joins(:notification_preference)
                           .where(notification_preferences: { email_alerts: true })
                           .find_each do |user|
        threshold = user.notification_preference.alert_form_abandonment_threshold || 70
        
        if abandonment_rate >= threshold
          NotificationJob.perform_later('form_abandonment_alert', user.id, form.id, abandonment_rate)
        end
      end
    end
  end

  def self.check_conversion_rate_drops
    Form.includes(website: :account).find_each do |form|
      current_rate = calculate_current_conversion_rate(form)
      previous_rate = calculate_previous_conversion_rate(form)
      
      next if current_rate.nil? || previous_rate.nil? || previous_rate == 0
      
      drop_percentage = ((previous_rate - current_rate) / previous_rate * 100).round(1)
      
      form.website.account.users.joins(:notification_preference)
                           .where(notification_preferences: { email_alerts: true })
                           .find_each do |user|
        threshold = user.notification_preference.alert_conversion_drop_threshold || 20
        
        if drop_percentage >= threshold
          NotificationJob.perform_later('conversion_drop_alert', user.id, form.id, current_rate, previous_rate)
        end
      end
    end
  end

  def self.check_low_traffic_alerts
    Form.includes(website: :account).find_each do |form|
      today_sessions = form.form_sessions.where(created_at: Date.current.all_day).count
      
      form.website.account.users.joins(:notification_preference)
                           .where(notification_preferences: { email_alerts: true })
                           .find_each do |user|
        threshold = user.notification_preference.alert_low_traffic_threshold || 10
        
        if today_sessions < threshold
          NotificationJob.perform_later('low_traffic_alert', user.id, form.id, today_sessions)
        end
      end
    end
  end

  def self.notify_new_insights
    # Find insights created in the last hour that haven't been notified
    recent_insights = Insight.includes(form: { website: :account })
                            .where('created_at > ? AND notified_at IS NULL', 1.hour.ago)

    recent_insights.find_each do |insight|
      insight.form.website.account.users.joins(:notification_preference)
                                  .where(notification_preferences: { email_insights: true })
                                  .find_each do |user|
        NotificationJob.perform_later('new_insight', user.id, insight.id)
      end
      
      # Mark as notified
      insight.update(notified_at: Time.current)
    end
  end

  private

  def self.calculate_current_conversion_rate(form)
    # Last 7 days conversion rate
    recent_sessions = form.form_sessions.where('created_at > ?', 7.days.ago)
    return nil if recent_sessions.count == 0
    
    conversions = recent_sessions.where(status: 'completed').count
    (conversions.to_f / recent_sessions.count * 100).round(1)
  end

  def self.calculate_previous_conversion_rate(form)
    # Previous 7 days conversion rate (8-14 days ago)
    previous_sessions = form.form_sessions.where(created_at: 14.days.ago..7.days.ago)
    return nil if previous_sessions.count == 0
    
    conversions = previous_sessions.where(status: 'completed').count
    (conversions.to_f / previous_sessions.count * 100).round(1)
  end
end