class WebhookNotificationService
  include HTT<PERSON>arty
  
  def initialize(webhook_url)
    @webhook_url = webhook_url
  end

  def send_form_abandonment_alert(form, abandonment_rate)
    payload = {
      event: 'form_abandonment_alert',
      timestamp: Time.current.iso8601,
      data: {
        form: {
          id: form.id,
          name: form.name,
          website: {
            id: form.website.id,
            domain: form.website.domain
          }
        },
        metrics: {
          abandonment_rate: abandonment_rate.round(2),
          recent_abandoned_sessions: form.form_sessions.where(status: 'abandoned').recent.count
        }
      }
    }
    
    send_webhook(payload)
  end

  def send_conversion_drop_alert(form, current_rate, previous_rate)
    drop_percentage = ((previous_rate - current_rate) / previous_rate * 100).round(1)
    
    payload = {
      event: 'conversion_drop_alert',
      timestamp: Time.current.iso8601,
      data: {
        form: {
          id: form.id,
          name: form.name,
          website: {
            id: form.website.id,
            domain: form.website.domain
          }
        },
        metrics: {
          current_conversion_rate: current_rate.round(2),
          previous_conversion_rate: previous_rate.round(2),
          drop_percentage: drop_percentage
        }
      }
    }
    
    send_webhook(payload)
  end

  def send_low_traffic_alert(form, session_count, threshold)
    payload = {
      event: 'low_traffic_alert',
      timestamp: Time.current.iso8601,
      data: {
        form: {
          id: form.id,
          name: form.name,
          website: {
            id: form.website.id,
            domain: form.website.domain
          }
        },
        metrics: {
          current_sessions: session_count,
          threshold: threshold,
          date: Date.current.iso8601
        }
      }
    }
    
    send_webhook(payload)
  end

  def send_new_insight(insight)
    payload = {
      event: 'new_insight',
      timestamp: Time.current.iso8601,
      data: {
        insight: {
          id: insight.id,
          type: insight.insight_type,
          severity: insight.severity,
          description: insight.description,
          recommendations: insight.recommendations,
          form: {
            id: insight.form.id,
            name: insight.form.name,
            website: {
              id: insight.form.website.id,
              domain: insight.form.website.domain
            }
          }
        }
      }
    }
    
    send_webhook(payload)
  end

  def send_weekly_summary(account, metrics)
    payload = {
      event: 'weekly_summary',
      timestamp: Time.current.iso8601,
      data: {
        account: {
          id: account.id,
          name: account.name
        },
        period: {
          start_date: 1.week.ago.beginning_of_week.iso8601,
          end_date: 1.week.ago.end_of_week.iso8601
        },
        metrics: {
          total_sessions: metrics[:total_sessions],
          total_conversions: metrics[:total_conversions],
          average_session_duration: metrics[:avg_session_duration],
          unique_visitors: metrics[:unique_visitors],
          conversion_rate: metrics[:total_sessions] > 0 ? (metrics[:total_conversions].to_f / metrics[:total_sessions] * 100).round(2) : 0
        }
      }
    }
    
    send_webhook(payload)
  end

  def test_webhook
    payload = {
      event: 'webhook_test',
      timestamp: Time.current.iso8601,
      data: {
        message: 'This is a test webhook from FormFlow Pro',
        status: 'success'
      }
    }
    
    send_webhook(payload)
  end

  private

  def send_webhook(payload)
    signature = generate_signature(payload)
    
    response = HTTParty.post(
      @webhook_url,
      body: payload.to_json,
      headers: {
        'Content-Type' => 'application/json',
        'X-FormFlow-Signature' => signature,
        'X-FormFlow-Event' => payload[:event],
        'User-Agent' => 'FormFlow-Pro-Webhook/1.0'
      },
      timeout: 30
    )

    unless (200..299).include?(response.code)
      Rails.logger.error "Webhook failed: #{response.code} - #{response.body}"
      raise "Webhook delivery failed: #{response.message}"
    end

    Rails.logger.info "Webhook delivered successfully to #{@webhook_url}"
    response
  rescue HTTParty::Error, Net::TimeoutError, StandardError => e
    Rails.logger.error "Webhook delivery error: #{e.message}"
    raise e
  end

  def generate_signature(payload)
    secret = Rails.application.credentials.dig(:webhook, :secret) || 'default-webhook-secret'
    OpenSSL::HMAC.hexdigest('SHA256', secret, payload.to_json)
  end
end